---
description:
globs:
alwaysApply: false
---
# Autonomous Agent CLI Tool - Workflow

## The Autonomous Agent Workflow

This document details the fully autonomous workflow of the AI-powered CLI tool, describing how it processes user input, interacts with AI models, executes actions, and maintains context - all without requiring user approval.

### Complete Workflow Cycle

#### 1. User Input Capture
- User enters a command or question into the CLI
- Input is captured and immediately passed to the Agent Engine
- The session context is loaded and attached to the request
- Semantic parsing of code-related queries begins immediately

#### 2. Agent Engine Processing
- The Agent Engine acts as the central orchestrator
- It maintains conversation state and workflow progression
- Coordinates all components throughout the execution flow
- No approval checkpoints are implemented, enabling full autonomy
- Determines if semantic code search, git operations, or debugging is required

#### 3. AI Provider Communication
- User request, conversation history, and available tools are packaged
- Package sent to configured LLM provider (OpenAI, Deepseek, or Ollama)
- Communication happens via the standardized OpenAI SDK 5.0.0
- Provider-specific endpoints and authentication are handled transparently
- Code context and embeddings are included for semantic understanding

#### 4. Streaming Response Processing
- AI responses stream in real-time rather than waiting for complete responses
- Responses are parsed for both content and tool call instructions
- Multiple tool calls within a single response are identified and queued
- Semantic reasoning about code is extracted from the response

#### 5. Tool Registry and Selection
- Tool Registry validates that requested tools exist and are available
- Tool signatures and required parameters are verified
- Tool selection is based on AI's determination of need, not user approval
- Specialized tools for semantic search, git operations, and debugging are registered

#### 6. Tool Call Parsing
- AI instructions in JSON format are parsed into executable actions
- Parameters are extracted and validated for completeness and correctness
- Invalid tool calls are logged but do not interrupt the workflow
- Complex parameter structures for semantic queries and git operations are handled

#### 7. Direct Command Execution
- Commands are executed immediately without user approval
- Shell commands run in the user's environment with full permissions
- File operations execute with complete access to the filesystem
- Tool chains execute in specified order (parallel or sequential)
- Git commands execute with repository awareness and history preservation
- Debugging operations connect to running processes when needed

#### 8. Semantic Code Analysis
- Code is analyzed for semantic meaning and intent beyond syntax
- Vector embeddings are generated for relevant code sections
- Semantic search queries are processed against the code embedding database
- Results are ranked by relevance and semantic similarity
- Code understanding informs debugging and git operations

#### 9. Git Operations Execution
- Repository state is captured before operations
- Git commands execute with appropriate safeguards
- Branch management, commits, and history operations are performed
- Results of git operations are captured for visualization
- Repository integrity is maintained throughout operations

#### 10. Change Tracking and Diff Generation
- Before file modifications, snapshots of the original content are captured
- After modifications, differences are computed using efficient diff algorithms
- Change records are stored with metadata (timestamp, operation type, content)
- Diffs are formatted for terminal visualization with syntax highlighting
- Git-aware diff generation for repository changes

#### 11. Runtime Debugging
- Application runtime is monitored for errors and exceptions
- Stack traces and error contexts are captured and analyzed
- Semantic understanding of code helps identify root causes
- Fix strategies are generated based on error analysis
- Fixes are automatically implemented and verified
- Debug sessions connect to live processes when needed

#### 12. Result Processing
- Command execution results are captured (output, errors, status codes)
- Results are formatted into structured data for AI consumption
- Success/failure is determined based on exit codes and error patterns
- Diff visualization is prepared for display in the terminal UI
- Semantic search results are formatted for presentation
- Git operation results and visualizations are prepared
- Debugging insights and fixes are documented

#### 13. Context Update
- Session context is updated with new information from command results
- Project structure changes are detected and indexed
- Memory is persisted for future sessions
- Change history with diffs is maintained for the session
- Code embeddings database is updated with new code
- Git repository state is tracked and updated
- Debug history and fixes are recorded for future reference

#### 14. Error Handling and Recovery
- Errors are detected and classified (temporary vs. permanent)
- Recovery strategies are applied based on error types
- Retry logic implements exponential backoff for rate limits
- Alternative approaches are attempted when initial attempts fail
- Diff analysis helps identify problematic changes that may have caused errors
- Semantic understanding helps generate more effective recovery strategies
- Git history enables reverting to stable states when needed
- Debugging system provides insights for error resolution

#### 15. UI Updates
- Terminal interface displays streaming AI responses in real-time
- Command execution and results are shown with appropriate formatting
- Progress indicators display during long-running operations
- No approval prompts interrupt the workflow
- Diff views show file changes with syntax highlighting and line-by-line comparison
- Semantic search results display with relevant code snippets
- Git visualizations show branch structures and changes
- Debug console shows runtime state and fix progress

#### 16. Continuous Operation Loop
- Results, diffs, and updated context are sent back to the AI
- AI analyzes results and changes, determining next steps without user intervention
- New tool calls are generated and executed automatically
- The loop continues until the original task is completed
- Semantic understanding improves with each iteration
- Git operations build on previous repository interactions
- Debugging insights inform future fix strategies

### Real-World Example Flow

When a user inputs: "Fix the authentication bug in the login component"

1. **Input Capture**: Command is received by the Agent Engine
2. **Semantic Analysis**: The request is analyzed to determine it requires code understanding
3. **Semantic Search**: Authentication-related code and login components are located
4. **Git Awareness**: Recent changes to authentication code are identified
5. **Code Understanding**: Authentication logic is analyzed semantically
6. **Debugging Activation**: Runtime testing of the login flow is initiated
7. **Error Detection**: Authentication failure points are identified
8. **Fix Generation**: Code fixes are developed based on semantic understanding
9. **Git Operation**: A new branch is created for the fix
10. **Code Modification**: The authentication bug is fixed in the appropriate files
11. **Verification**: The fix is tested through the debugging system
12. **Git Commit**: Changes are committed with an appropriate message
13. **Result Presentation**: The user sees the entire process with diffs and explanations
14. **Semantic Update**: Code embeddings are updated with the fixed code

### Key Differences from Approval-Based Systems

1. **No Approval Checkpoints**: All tool calls execute immediately
2. **Continuous Flow**: No interruptions in the execution process
3. **Full Environment Access**: Direct access to shell and filesystem
4. **Autonomous Decision Making**: AI determines and executes next steps
5. **Complete Task Chains**: Full tasks completed without user confirmation
6. **Integrated Diff Visualization**: Changes are tracked and displayed automatically
7. **Semantic Code Navigation**: Deep understanding of code without explicit guidance
8. **Git Workflow Automation**: Complete git operations without approval steps
9. **Autonomous Debugging**: End-to-end debugging without user intervention

### Diff View Integration

The diff view system is tightly integrated into the autonomous workflow:

1. **Automatic Tracking**: All file operations are automatically tracked for diff generation
2. **Real-Time Updates**: Diffs are generated and displayed in real-time as changes occur
3. **Contextual Awareness**: The AI receives diff information to understand the changes it made
4. **Terminal Rendering**: Diffs are rendered in the terminal with syntax highlighting and clear formatting
5. **Historical View**: Users can access the history of changes with corresponding diffs
6. **Semantic Analysis**: Diffs include semantic understanding of the changes, not just text differences
7. **Git-Aware Diffs**: Diff visualization is aware of git history and can show changes across commits

### Semantic Code Search Implementation

The semantic code search capability functions as follows:

1. **Code Embedding Generation**: Code files are processed to create semantic vector embeddings
2. **Query Understanding**: Natural language queries are parsed for semantic intent
3. **Vector Similarity**: Query vectors are compared to code embeddings for relevance
4. **Context-Aware Results**: Search results incorporate surrounding code context
5. **Relevance Ranking**: Results are ranked by semantic similarity and importance
6. **Code Navigation**: Search results enable direct navigation to relevant code
7. **Continuous Learning**: The semantic understanding improves with usage patterns

### Git Integration Workflow

The git integration system provides seamless version control capabilities:

1. **Repository Awareness**: Automatic detection and understanding of git repositories
2. **State Preservation**: Repository state is tracked and preserved throughout operations
3. **Branch Management**: Creating, switching, and merging branches as needed
4. **Commit Automation**: Generating meaningful commit messages and organizing changes
5. **History Navigation**: Understanding project history to inform current operations
6. **Conflict Resolution**: Automatically resolving merge conflicts when possible
7. **Visual Representation**: Providing clear visualization of repository structure

### Interactive Debugging System

The debugging system works autonomously to find and fix issues:

1. **Error Detection**: Identifying runtime errors and exceptions in code
2. **Contextual Analysis**: Understanding the execution context where errors occur
3. **Root Cause Identification**: Using semantic code understanding to find underlying issues
4. **Fix Generation**: Creating appropriate fixes based on error analysis
5. **Test Verification**: Automatically testing fixes to verify effectiveness
6. **Runtime Monitoring**: Connecting to running processes to observe behavior
7. **Fix Application**: Implementing fixes and committing changes when successful

### Safety and Security Considerations

Despite removing approval requirements, the system implements:

1. **Runtime Monitoring**: Detecting abnormal resource usage or behavior
2. **Logging**: Complete audit trail of all executed actions
3. **Resource Limits**: Preventing excessive resource consumption
4. **Error Detection**: Identifying potentially harmful outcomes
5. **Isolation**: Some operations run in controlled environments when possible
6. **Comprehensive Diff Records**: All changes are recorded with detailed diffs for accountability
7. **Git Safeguards**: Repository integrity protection during git operations
8. **Semantic Safety Analysis**: Evaluating code changes for potential security issues
9. **Debug Environment Controls**: Limiting the scope and impact of debugging operations

This workflow creates a truly autonomous agent that can understand, plan, and execute complex tasks without requiring user confirmation at each step, dramatically increasing efficiency while maintaining appropriate security measures and providing clear visibility into all changes through integrated diff visualization, semantic code understanding, git integration, and autonomous debugging capabilities.
