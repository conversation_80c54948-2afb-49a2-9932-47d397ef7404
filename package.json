{"name": "autonomous-agent-cli", "version": "1.0.0", "description": "A fully autonomous, AI-powered CLI tool that operates in the user's local environment", "main": "dist/index.js", "type": "module", "bin": {"agent": "./dist/index.js"}, "scripts": {"start": "node dist/index.js", "dev": "tsx watch src/index.ts", "build": "tsc", "lint": "eslint . --ext .ts", "test": "vitest run", "test:watch": "vitest"}, "keywords": ["cli", "agent", "ai", "autonomous", "openai", "deepseek", "ollama", "semantic", "diff", "git", "debug"], "author": "", "license": "MIT", "dependencies": {"@clack/prompts": "^0.7.0", "@dqbd/tiktoken": "^1.0.7", "@napi-rs/canvas": "^0.1.44", "@oclif/core": "^3.16.0", "@types/diff": "^5.0.9", "boxen": "^7.1.1", "chalk": "^5.3.0", "chokidar": "^3.5.3", "commander": "^11.1.0", "diff": "^5.1.0", "dotenv": "^16.3.1", "enquirer": "^2.4.1", "execa": "^8.0.1", "fast-glob": "^3.3.2", "find-up": "^6.3.0", "got": "^13.0.0", "ink": "^4.4.1", "ink-spinner": "^5.0.0", "ioredis": "^5.3.2", "isomorphic-git": "^1.25.0", "keyv": "^4.5.4", "langchain": "^0.0.214", "lokijs": "^1.5.12", "micromatch": "^4.0.5", "node-fetch": "^3.3.2", "node-pty": "^1.0.0", "openai": "^5.0.0", "ora": "^7.0.1", "p-limit": "^4.0.0", "picocolors": "^1.0.0", "prettier": "^3.0.3", "redis": "^4.6.11", "redis-om": "^0.4.2", "simple-git": "^3.20.0", "sqlite": "^5.1.1", "sqlite3": "^5.1.6", "strip-ansi": "^7.1.0", "tsx": "^3.14.0", "typedoc": "^0.25.3", "vscode-langservers-extracted": "^4.8.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.38", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "ts-node": "^10.9.1", "typescript": "^5.3.2", "vitest": "^0.34.6"}, "engines": {"node": ">=18.0.0"}}