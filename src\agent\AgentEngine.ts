import { ToolRegistry } from '../tools/ToolRegistry.js';
import { ContextManager } from '../context/ContextManager.js';
import { StreamingResponseHandler } from '../ai/StreamingResponseHandler.js';
import { ProviderManager } from '../ai/ProviderManager.js';
import { DirectoryIndexer } from '../context/DirectoryIndexer.js';
import { ToolCallParser } from '../tools/ToolCallParser.js';
import { ToolChainExecutor } from '../tools/ToolChainExecutor.js';
import { EventEmitter } from 'events';
import { DiffManager } from '../diff/DiffManager.js';
import { GitManager } from '../git/GitManager.js';
import { SemanticSearchEngine } from '../semantic/SemanticSearchEngine.js';
import { DebugManager } from '../debug/DebugManager.js';
import { logger } from '../utils/logger.js';
import { Config } from '../utils/config.js';

export interface AgentEngineOptions {
  providers: ProviderManager;
  toolRegistry: ToolRegistry;
  config: Config;
}

export class AgentEngine extends EventEmitter {
  private providers: ProviderManager;
  private toolRegistry: ToolRegistry;
  private contextManager: ContextManager;
  private directoryIndexer: DirectoryIndexer;
  private toolCallParser: ToolCallParser;
  private toolChainExecutor: ToolChainExecutor;
  private diffManager: DiffManager;
  private gitManager: GitManager;
  private semanticSearch: SemanticSearchEngine;
  private debugManager: DebugManager;
  private isProcessing: boolean = false;
  private config: Config;

  constructor(options: AgentEngineOptions) {
    super();
    this.providers = options.providers;
    this.toolRegistry = options.toolRegistry;
    this.config = options.config;
    
    // Initialize subsystems
    this.contextManager = new ContextManager();
    this.directoryIndexer = new DirectoryIndexer(this.contextManager);
    this.toolCallParser = new ToolCallParser(this.toolRegistry);
    this.toolChainExecutor = new ToolChainExecutor();
    this.diffManager = new DiffManager();
    this.gitManager = new GitManager(this.diffManager);
    this.semanticSearch = new SemanticSearchEngine(this.contextManager);
    this.debugManager = new DebugManager(this.semanticSearch);
    
    // Setup event listeners
    this.setupEventListeners();
    
    logger.info('Agent Engine initialized');
  }
  
  private setupEventListeners(): void {
    // Listen for tool execution results
    this.toolChainExecutor.on('toolExecuted', (result) => {
      this.emit('toolExecuted', result);
      this.contextManager.updateContext({
        lastToolExecution: {
          tool: result.tool,
          status: result.success ? 'success' : 'failure',
          result: result.output
        }
      });
    });
    
    // Listen for context updates
    this.contextManager.on('contextUpdated', (context) => {
      this.emit('contextUpdated', context);
    });
    
    // Listen for file changes
    this.directoryIndexer.on('fileChanged', (filePath) => {
      this.emit('fileChanged', filePath);
      this.diffManager.trackFileChange(filePath);
    });
    
    // Listen for diff generation
    this.diffManager.on('diffGenerated', (diff) => {
      this.emit('diffGenerated', diff);
      this.contextManager.updateContext({
        lastDiff: diff
      });
    });
    
    // Listen for git operations
    this.gitManager.on('gitOperation', (operation) => {
      this.emit('gitOperation', operation);
      this.contextManager.updateContext({
        lastGitOperation: operation
      });
    });
    
    // Listen for semantic search results
    this.semanticSearch.on('searchResults', (results) => {
      this.emit('semanticSearchResults', results);
      this.contextManager.updateContext({
        lastSemanticSearch: {
          query: results.query,
          results: results.matches
        }
      });
    });
    
    // Listen for debug events
    this.debugManager.on('debugEvent', (event) => {
      this.emit('debugEvent', event);
      this.contextManager.updateContext({
        lastDebugEvent: event
      });
    });
  }
  
  public async initialize(): Promise<void> {
    try {
      // Initialize directory indexing
      await this.directoryIndexer.indexCurrentDirectory();
      
      // Initialize git if available
      await this.gitManager.initialize();
      
      // Initialize semantic search
      await this.semanticSearch.initialize();
      
      // Load persisted context if available
      await this.contextManager.loadPersistedContext();
      
      logger.info('Agent Engine fully initialized');
      this.emit('initialized');
    } catch (error) {
      logger.error('Error initializing agent engine:', error);
      throw error;
    }
  }
  
  public async process(input: string): Promise<void> {
    if (this.isProcessing) {
      logger.warn('Already processing a request. Please wait.');
      return;
    }
    
    this.isProcessing = true;
    this.emit('processingStarted', input);
    
    try {
      logger.info(`Processing user input: ${input}`);
      
      // Update context with user input
      this.contextManager.updateContext({
        lastUserInput: input,
        timestamp: Date.now()
      });
      
      // Check if semantic code search is needed
      if (this.shouldPerformSemanticSearch(input)) {
        await this.performSemanticSearch(input);
      }
      
      // Check if git operations might be needed
      if (this.mightRequireGitOperations(input)) {
        await this.gitManager.updateRepositoryState();
      }
      
      // Prepare context for AI request
      const context = this.contextManager.getFullContext();
      
      // Get available tools
      const availableTools = this.toolRegistry.getAllTools();
      
      // Send request to AI provider
      const responseHandler = new StreamingResponseHandler();
      
      responseHandler.on('content', (content) => {
        this.emit('aiResponse', content);
      });
      
      responseHandler.on('toolCall', async (toolCall) => {
        try {
          // Parse tool call
          const parsedToolCall = this.toolCallParser.parse(toolCall);
          
          if (parsedToolCall) {
            // Emit tool call event
            this.emit('toolCallReceived', parsedToolCall);
            
            // Execute tool immediately (no approval flow)
            const result = await this.toolChainExecutor.executeToolCall(parsedToolCall);
            
            // Send result back to responseHandler
            responseHandler.handleToolCallResult(toolCall.id, result);
          }
        } catch (error) {
          logger.error('Error processing tool call:', error);
          responseHandler.handleToolCallError(toolCall.id, error);
        }
      });
      
      // Get the appropriate provider based on configuration
      const provider = this.providers.getCurrentProvider();
      
      // Start the request
      await provider.sendRequest({
        input,
        context,
        tools: availableTools,
        responseHandler,
        config: this.config
      });
      
      // Wait for response handler to complete
      await responseHandler.waitForCompletion();
      
      // Update directory index to capture any changes
      await this.directoryIndexer.refreshIndex();
      
      logger.info('Processing completed');
      this.emit('processingCompleted');
    } catch (error) {
      logger.error('Error processing input:', error);
      this.emit('processingError', error);
    } finally {
      this.isProcessing = false;
    }
  }
  
  private shouldPerformSemanticSearch(input: string): boolean {
    // Analyze input to determine if semantic search is needed
    const codeRelatedTerms = [
      'code', 'function', 'class', 'method', 'component',
      'bug', 'fix', 'implement', 'refactor', 'optimize',
      'file', 'directory', 'module', 'import', 'export'
    ];
    
    return codeRelatedTerms.some(term => input.toLowerCase().includes(term));
  }
  
  private async performSemanticSearch(input: string): Promise<void> {
    try {
      await this.semanticSearch.search(input);
    } catch (error) {
      logger.error('Error performing semantic search:', error);
    }
  }
  
  private mightRequireGitOperations(input: string): boolean {
    // Analyze input to determine if git operations might be needed
    const gitRelatedTerms = [
      'git', 'commit', 'branch', 'merge', 'checkout',
      'pull', 'push', 'clone', 'repository', 'repo',
      'version control', 'history', 'changes'
    ];
    
    return gitRelatedTerms.some(term => input.toLowerCase().includes(term));
  }
  
  public getContextManager(): ContextManager {
    return this.contextManager;
  }
  
  public getToolRegistry(): ToolRegistry {
    return this.toolRegistry;
  }
  
  public getDiffManager(): DiffManager {
    return this.diffManager;
  }
  
  public getGitManager(): GitManager {
    return this.gitManager;
  }
  
  public getSemanticSearch(): SemanticSearchEngine {
    return this.semanticSearch;
  }
  
  public getDebugManager(): DebugManager {
    return this.debugManager;
  }
} 