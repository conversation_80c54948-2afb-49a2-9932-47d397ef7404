import { OpenAI } from 'openai';
import { BaseProvider, RequestOptions } from './BaseProvider.js';
import { Config } from '../../utils/config.js';
import { logger } from '../../utils/logger.js';
import { Tool } from '../../tools/ToolRegistry.js';

/**
 * Deepseek Provider implementation using OpenAI SDK with custom endpoint
 */
export class DeepseekProvider extends BaseProvider {
  private client: OpenAI;

  constructor(config: Config) {
    super(config);
    this.initializeClient();
  }

  /**
   * Initialize Deepseek client using OpenAI SDK
   */
  private initializeClient(): void {
    this.client = new OpenAI({
      apiKey: this.apiKey,
      baseURL: this.baseUrl,
    });
    logger.debug('Deepseek client initialized');
  }

  /**
   * Send a request to Deepseek
   */
  public async sendRequest(options: RequestOptions): Promise<void> {
    const { input, context, tools, responseHandler, config } = options;

    try {
      // Format tools for Deepseek (using OpenAI format)
      const formattedTools = this.formatTools(tools);

      // Prepare messages
      const messages = this.prepareMessages(input, context);

      // Create stream
      const stream = await this.client.chat.completions.create({
        model: this.model,
        messages,
        tools: formattedTools,
        stream: true,
        temperature: config.ai.temperature || 0.7,
        max_tokens: config.ai.maxTokens || 4000,
      });

      // Process the stream
      for await (const chunk of stream) {
        // Process content deltas
        if (chunk.choices[0]?.delta?.content) {
          responseHandler.handleContentChunk(chunk.choices[0].delta.content);
        }

        // Process tool calls
        if (chunk.choices[0]?.delta?.tool_calls) {
          for (const toolCall of chunk.choices[0].delta.tool_calls) {
            if (toolCall.id && toolCall.function) {
              responseHandler.handleToolCallChunk({
                id: toolCall.id,
                name: toolCall.function.name || '',
                arguments: toolCall.function.arguments || '',
              });
            }
          }
        }

        // Check if the response is done
        if (chunk.choices[0]?.finish_reason === 'stop') {
          responseHandler.complete();
          break;
        }
      }
    } catch (error) {
      logger.error('Error in Deepseek request:', error);
      responseHandler.handleError(error);
    }
  }

  /**
   * Format tools for Deepseek
   */
  private formatTools(tools: Tool[]): any[] {
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters,
      }
    }));
  }

  /**
   * Prepare messages for Deepseek
   */
  private prepareMessages(input: string, context: Record<string, any>): any[] {
    const messages = [];

    // Add system message
    messages.push({
      role: 'system',
      content: this.config.ai.systemPrompt || 'You are a helpful assistant.'
    });

    // Add context messages if available
    if (context.conversationHistory && Array.isArray(context.conversationHistory)) {
      messages.push(...context.conversationHistory);
    }

    // Add current user message
    messages.push({
      role: 'user',
      content: input
    });

    return messages;
  }

  /**
   * Extract API key from config
   */
  protected getApiKeyFromConfig(config: Config): string {
    return config.ai.providers.deepseek.apiKey || process.env.DEEPSEEK_API_KEY || '';
  }

  /**
   * Extract base URL from config
   */
  protected getBaseUrlFromConfig(config: Config): string {
    return config.ai.providers.deepseek.baseUrl || 'https://api.deepseek.com/v1';
  }
} 