import { BaseProvider, RequestOptions } from './BaseProvider.js';
import { Config } from '../../utils/config.js';
import { logger } from '../../utils/logger.js';
import { Tool } from '../../tools/ToolRegistry.js';

/**
 * Ollama Provider implementation for local model deployment
 */
export class OllamaProvider extends BaseProvider {
  constructor(config: Config) {
    super(config);
    logger.debug('Ollama provider initialized');
  }

  /**
   * Send a request to Ollama
   */
  public async sendRequest(options: RequestOptions): Promise<void> {
    const { input, context, tools, responseHandler, config } = options;

    try {
      // Format tools for Ollama (JSON format)
      const formattedTools = this.formatTools(tools);

      // Prepare messages
      const messages = this.prepareMessages(input, context);

      // Prepare request body
      const requestBody = {
        model: this.model,
        messages,
        tools: formattedTools,
        stream: true,
        options: {
          temperature: config.ai.temperature || 0.7,
        }
      };

      // Create fetch request with streaming
      const response = await fetch(`${this.baseUrl}/api/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`Ollama API error: ${response.status} ${response.statusText}`);
      }

      if (!response.body) {
        throw new Error('Ollama API returned empty response body');
      }

      // Process the stream
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          responseHandler.complete();
          break;
        }

        // Decode the chunk and add to buffer
        buffer += decoder.decode(value, { stream: true });

        // Process complete JSON objects from buffer
        let jsonStart = 0;
        let jsonEnd = buffer.indexOf('\n', jsonStart);

        while (jsonEnd !== -1) {
          const jsonStr = buffer.substring(jsonStart, jsonEnd).trim();
          
          if (jsonStr) {
            try {
              const chunk = JSON.parse(jsonStr);
              
              // Process content
              if (chunk.message?.content) {
                responseHandler.handleContentChunk(chunk.message.content);
              }

              // Process tool calls
              if (chunk.message?.tool_calls) {
                for (const toolCall of chunk.message.tool_calls) {
                  responseHandler.handleToolCallChunk({
                    id: toolCall.id,
                    name: toolCall.function.name,
                    arguments: toolCall.function.arguments,
                  });
                }
              }

              // Check if done
              if (chunk.done) {
                responseHandler.complete();
              }
            } catch (err) {
              logger.error('Error parsing Ollama response chunk:', err);
            }
          }

          // Move to next JSON object
          jsonStart = jsonEnd + 1;
          jsonEnd = buffer.indexOf('\n', jsonStart);
        }

        // Keep remaining partial JSON in buffer
        buffer = buffer.substring(jsonStart);
      }
    } catch (error) {
      logger.error('Error in Ollama request:', error);
      responseHandler.handleError(error);
    }
  }

  /**
   * Format tools for Ollama
   */
  private formatTools(tools: Tool[]): any[] {
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters,
      }
    }));
  }

  /**
   * Prepare messages for Ollama
   */
  private prepareMessages(input: string, context: Record<string, any>): any[] {
    const messages = [];

    // Add system message
    messages.push({
      role: 'system',
      content: this.config.ai.systemPrompt || 'You are a helpful assistant.'
    });

    // Add context messages if available
    if (context.conversationHistory && Array.isArray(context.conversationHistory)) {
      messages.push(...context.conversationHistory);
    }

    // Add current user message
    messages.push({
      role: 'user',
      content: input
    });

    return messages;
  }

  /**
   * Extract API key from config (not needed for Ollama)
   */
  protected getApiKeyFromConfig(config: Config): string {
    return '';
  }

  /**
   * Extract base URL from config
   */
  protected getBaseUrlFromConfig(config: Config): string {
    return config.ai.providers.ollama.baseUrl || 'http://localhost:11434';
  }
} 