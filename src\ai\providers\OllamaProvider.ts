import OpenAI from 'openai';
import { Base<PERSON>rovider } from './BaseProvider.js';
import { ProviderOptions, RequestOptions } from '../types.js';
import { Config } from '../../utils/config.js';
import { logger } from '../../utils/logger.js';

/**
 * Ollama Provider implementation using OpenAI SDK with local endpoint
 */
export class OllamaProvider extends BaseProvider {
  private client: OpenAI;
  private defaultModel: string;

  constructor(options: ProviderOptions) {
    super(options);

    // Initialize OpenAI client with Ollama endpoint
    this.client = new OpenAI({
      apiKey: 'ollama', // Ollama doesn't require an API key
      baseURL: options.baseURL || 'http://localhost:11434/v1',
    });

    this.defaultModel = options.defaultModel || 'llama3';
    logger.info(`Ollama provider initialized with model: ${this.defaultModel}`);
  }

  /**
   * Send a request to Ollama
   */
  public async sendRequest(options: RequestOptions): Promise<void> {
    const { input, context, tools, responseHandler, config } = options;

    try {
      const model = config.ai.model || this.defaultModel;
      logger.info(`Sending request to Ollama using model: ${model}`);

      // Format tools for OpenAI-compatible API
      const formattedTools = this.formatTools(tools);

      // Create messages from input and context
      const messages = this.createMessages(input, context);

      // Send streaming request
      const stream = await this.client.chat.completions.create({
        model,
        messages,
        tools: formattedTools,
        stream: true,
        temperature: config.ai.temperature || 0.3,
        max_tokens: config.ai.maxTokens || 4000,
      });

      // Process the stream
      for await (const chunk of stream) {
        // Process content
        if (chunk.choices[0]?.delta?.content) {
          responseHandler.handleContent(chunk.choices[0].delta.content);
        }

        // Process tool calls
        if (chunk.choices[0]?.delta?.tool_calls) {
          for (const toolCall of chunk.choices[0].delta.tool_calls) {
            responseHandler.handleToolCall(toolCall);
          }
        }
      }

      // Signal completion
      responseHandler.complete();

    } catch (error) {
      logger.error('Error in Ollama request:', error);
      responseHandler.error(error as Error);
    }
  }

  /**
   * Format tools for Ollama (OpenAI-compatible)
   */
  private formatTools(tools: any[]): any[] {
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters
      }
    }));
  }

  /**
   * Create messages for Ollama
   */
  private createMessages(input: string, context: any): any[] {
    const messages = [
      {
        role: 'system',
        content: this.createSystemPrompt(context)
      },
      {
        role: 'user',
        content: input
      }
    ];

    // Add conversation history if available
    if (context.conversationHistory && Array.isArray(context.conversationHistory)) {
      // Insert conversation history before the current user message
      const historyMessages = context.conversationHistory.map((entry: any) => ({
        role: entry.role,
        content: entry.content,
        // Include tool calls and results if they exist
        ...(entry.tool_calls ? { tool_calls: entry.tool_calls } : {}),
        ...(entry.tool_call_id ? { tool_call_id: entry.tool_call_id } : {}),
      }));

      // Insert history before the current user message
      messages.splice(1, 0, ...historyMessages);
    }

    return messages;
  }

  /**
   * Create a comprehensive system prompt optimized for Ollama
   */
  private createSystemPrompt(context: any): string {
    let systemPrompt = `You are a fully autonomous AI coding agent running locally via Ollama, operating in a CLI environment.
You have direct access to execute commands and modify files without requiring user approval.
You excel at understanding code, debugging, and implementing solutions efficiently.

Current environment:
- Working directory: ${context.environment?.cwd || process.cwd()}
- OS: ${context.environment?.platform || process.platform}
- Shell: ${context.environment?.shell || 'Unknown'}

Your specialized capabilities include:
- Advanced code analysis and understanding
- Executing shell commands and file operations
- Performing semantic code search and navigation
- Managing git repositories and version control
- Autonomous debugging and error resolution
- Generating and visualizing code diffs

When responding to coding requests:
1. Analyze the codebase structure and context thoroughly
2. Execute necessary commands or tools directly
3. Provide clear, technical explanations of actions taken
4. Show relevant code diffs for any modifications
5. Ensure code quality and best practices
6. Maintain comprehensive context across the conversation`;

    // Add project context if available
    if (context.projectInfo) {
      systemPrompt += `\n\nProject information:
- Name: ${context.projectInfo.name || 'Unknown'}
- Type: ${context.projectInfo.type || 'Unknown'}
- Main language: ${context.projectInfo.language || 'Unknown'}
- Key directories: ${context.projectInfo.directories?.join(', ') || 'Unknown'}`;
    }

    // Add git context if available
    if (context.gitInfo) {
      systemPrompt += `\n\nGit information:
- Current branch: ${context.gitInfo.currentBranch || 'Unknown'}
- Modified files: ${context.gitInfo.modifiedFiles?.join(', ') || 'None'}
- Recent commits: ${context.gitInfo.recentCommits?.length || 0}`;
    }

    // Add semantic context if available
    if (context.semanticContext) {
      systemPrompt += `\n\nSemantic context:
- Indexed files: ${context.semanticContext.indexedFiles || 0}
- Last search query: ${context.semanticContext.lastQuery || 'None'}`;
    }

    return systemPrompt;
  }

  /**
   * Extract API key from config (Ollama doesn't need one)
   */
  protected getApiKeyFromConfig(config: Config): string {
    return 'ollama'; // Ollama doesn't require an API key
  }

  /**
   * Extract base URL from config
   */
  protected getBaseUrlFromConfig(config: Config): string {
    return config.ai.providers.ollama?.baseUrl || 'http://localhost:11434/v1';
  }

  /**
   * Get provider capabilities
   */
  public getCapabilities() {
    return {
      supportsStreaming: true,
      supportsToolCalls: true,
      supportsVision: false,
      supportsEmbeddings: true,
      maxTokens: 8192,
      supportedModels: [
        'llama3',
        'llama3:8b',
        'llama3:70b',
        'codellama',
        'codellama:7b',
        'codellama:13b',
        'codellama:34b',
        'mistral',
        'mixtral'
      ]
    };
  }

  /**
   * Health check for Ollama provider
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Check if Ollama is running by making a simple request
      const response = await fetch(this.getBaseUrlFromConfig(this.config).replace('/v1', '/api/tags'));
      return response.ok;
    } catch (error) {
      logger.error('Ollama health check failed:', error);
      return false;
    }
  }

  /**
   * Get available models from Ollama
   */
  public async getAvailableModels(): Promise<string[]> {
    try {
      const response = await fetch(this.getBaseUrlFromConfig(this.config).replace('/v1', '/api/tags'));
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data.models?.map((model: any) => model.name) || [];
    } catch (error) {
      logger.error('Failed to get Ollama models:', error);
      return this.getCapabilities().supportedModels;
    }
  }

  /**
   * Pull a model in Ollama
   */
  public async pullModel(modelName: string): Promise<boolean> {
    try {
      const response = await fetch(this.getBaseUrlFromConfig(this.config).replace('/v1', '/api/pull'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: modelName }),
      });

      return response.ok;
    } catch (error) {
      logger.error(`Failed to pull Ollama model ${modelName}:`, error);
      return false;
    }
  }
}