import OpenAI from 'openai';
import { BaseProvider } from './BaseProvider.js';
import { StreamingResponseHandler } from '../StreamingResponseHandler.js';
import { ProviderOptions, RequestOptions } from '../types.js';
import { logger } from '../../utils/logger.js';

/**
 * OpenAI Provider implementation
 */
export class OpenA<PERSON>rovider extends BaseProvider {
  private client: OpenAI;
  private defaultModel: string;

  constructor(options: ProviderOptions) {
    super(options);
    
    // Initialize OpenAI client
    this.client = new OpenAI({
      apiKey: options.apiKey,
      baseURL: options.baseURL || undefined,
      organization: options.organization || undefined,
    });
    
    this.defaultModel = options.defaultModel || 'gpt-4o';
    logger.info(`OpenAI provider initialized with model: ${this.defaultModel}`);
  }

  /**
   * Send a request to OpenAI
   */
  public async sendRequest(options: RequestOptions): Promise<void> {
    const { input, context, tools, responseHandler, config } = options;
    
    try {
      const model = config.model || this.defaultModel;
      logger.info(`Sending request to OpenAI using model: ${model}`);
      
      // Format tools for OpenAI
      const formattedTools = this.formatTools(tools);
      
      // Create messages from input and context
      const messages = this.createMessages(input, context);
      
      // Send streaming request
      const stream = await this.client.chat.completions.create({
        model,
        messages,
        tools: formattedTools,
        stream: true,
        temperature: config.temperature || 0.2,
      });
      
      // Process the stream
      for await (const chunk of stream) {
        // Process content
        if (chunk.choices[0]?.delta?.content) {
          responseHandler.handleContent(chunk.choices[0].delta.content);
        }
        
        // Process tool calls
        if (chunk.choices[0]?.delta?.tool_calls) {
          for (const toolCall of chunk.choices[0].delta.tool_calls) {
            responseHandler.handleToolCall(toolCall);
          }
        }
      }
      
      // Signal completion
      responseHandler.complete();
      
    } catch (error) {
      logger.error('Error in OpenAI request:', error);
      responseHandler.error(error);
    }
  }

  /**
   * Format tools for OpenAI
   */
  private formatTools(tools: any[]): any[] {
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters
      }
    }));
  }

  /**
   * Create messages for OpenAI
   */
  private createMessages(input: string, context: any): any[] {
    const messages = [
      {
        role: 'system',
        content: this.createSystemPrompt(context)
      },
      {
        role: 'user',
        content: input
      }
    ];
    
    // Add conversation history if available
    if (context.conversationHistory && Array.isArray(context.conversationHistory)) {
      // Insert conversation history before the current user message
      const historyMessages = context.conversationHistory.map((entry: any) => ({
        role: entry.role,
        content: entry.content,
        // Include tool calls and results if they exist
        ...(entry.tool_calls ? { tool_calls: entry.tool_calls } : {}),
        ...(entry.tool_call_id ? { tool_call_id: entry.tool_call_id } : {}),
      }));
      
      // Insert history before the current user message
      messages.splice(1, 0, ...historyMessages);
    }
    
    return messages;
  }

  /**
   * Create a comprehensive system prompt
   */
  private createSystemPrompt(context: any): string {
    // Create a comprehensive system prompt that includes:
    // 1. Agent capabilities and behavior instructions
    // 2. Current environment context (working directory, OS, etc.)
    // 3. Project context (if available)
    
    let systemPrompt = `You are a fully autonomous AI agent operating in a CLI environment. 
You have direct access to execute commands and modify files without requiring user approval.
You should always strive to complete tasks efficiently and accurately.

Current environment:
- Working directory: ${context.currentDirectory || 'Unknown'}
- OS: ${context.os || 'Unknown'}
- Shell: ${context.shell || 'Unknown'}

Your capabilities include:
- Executing shell commands
- Reading and modifying files
- Performing semantic code search
- Managing git repositories
- Debugging applications
- Visualizing diffs between file changes

When responding to user requests:
1. Analyze the request thoroughly
2. Execute necessary commands or tools directly
3. Provide clear, concise feedback on actions taken
4. Show relevant diffs for any file changes
5. Always maintain context across the conversation`;

    // Add project context if available
    if (context.projectInfo) {
      systemPrompt += `\n\nProject information:
- Name: ${context.projectInfo.name || 'Unknown'}
- Type: ${context.projectInfo.type || 'Unknown'}
- Main language: ${context.projectInfo.language || 'Unknown'}
- Key directories: ${context.projectInfo.directories?.join(', ') || 'Unknown'}`;
    }
    
    // Add git context if available
    if (context.gitInfo) {
      systemPrompt += `\n\nGit information:
- Current branch: ${context.gitInfo.currentBranch || 'Unknown'}
- Modified files: ${context.gitInfo.modifiedFiles?.join(', ') || 'None'}`;
    }
    
    return systemPrompt;
  }
} 