import { ProviderManager } from './ProviderManager.js';
import { ProviderType } from './types.js';
import { Config } from '../utils/config.js';
import { logger } from '../utils/logger.js';

/**
 * Setup AI providers
 * @param config Configuration object
 * @param providerOverride Optional provider override
 * @param modelOverride Optional model override
 * @returns Initialized ProviderManager
 */
export async function setupProviders(
  config: Config,
  providerOverride?: string,
  modelOverride?: string
): Promise<ProviderManager> {
  try {
    // Apply overrides to config if provided
    let updatedConfig = { ...config };
    
    if (providerOverride) {
      updatedConfig = {
        ...updatedConfig,
        ai: {
          ...updatedConfig.ai,
          defaultProvider: providerOverride
        }
      };
      logger.info(`Provider override: ${providerOverride}`);
    }
    
    if (modelOverride) {
      updatedConfig = {
        ...updatedConfig,
        ai: {
          ...updatedConfig.ai,
          model: modelOverride
        }
      };
      logger.info(`Model override: ${modelOverride}`);
    }
    
    // Initialize provider manager
    const providerManager = new ProviderManager(updatedConfig);
    
    // Ensure we have at least one provider
    const availableProviders = providerManager.getAvailableProviders();
    
    if (availableProviders.length === 0) {
      throw new Error('No AI providers are available. Please configure at least one provider.');
    }
    
    logger.info(`Available providers: ${availableProviders.join(', ')}`);
    
    return providerManager;
  } catch (error) {
    logger.error('Error setting up providers:', error);
    throw error;
  }
} 