import { StreamingResponseHandler } from './StreamingResponseHandler.js';
import { Config } from '../utils/config.js';

/**
 * Provider options for initializing AI providers
 */
export interface ProviderOptions {
  apiKey: string;
  defaultModel?: string;
  baseURL?: string;
  organization?: string;
  config?: Config;
}

/**
 * Request options for sending requests to AI providers
 */
export interface RequestOptions {
  input: string;
  context: any;
  tools: any[];
  responseHandler: StreamingResponseHandler;
  config: Config;
}

/**
 * Tool call from AI response
 */
export interface ToolCall {
  id: string;
  name: string;
  arguments: string;
}

/**
 * Tool call result
 */
export interface ToolCallResult {
  success: boolean;
  output: any;
  error?: Error;
}

/**
 * Provider type
 */
export enum ProviderType {
  OPENAI = 'openai',
  DEEPSEEK = 'deepseek',
  OLLAMA = 'ollama'
} 