import { EventEmitter } from 'events';
import fs from 'fs/promises';
import path from 'path';
import os from 'os';
import { logger } from '../utils/logger.js';

/**
 * Context Manager class for maintaining and updating context across sessions
 */
export class ContextManager extends EventEmitter {
  private context: Record<string, any> = {};
  private persistPath: string;
  private memoryLimit: number;
  
  constructor(options?: { persistPath?: string; memoryLimit?: number }) {
    super();
    
    // Default to storing context in user's home directory
    this.persistPath = options?.persistPath || path.join(os.homedir(), '.agent-cli', 'context.json');
    
    // Default memory limit of 100MB to prevent excessive context growth
    this.memoryLimit = options?.memoryLimit || 100 * 1024 * 1024; // 100MB
    
    // Initialize with empty context
    this.context = {
      conversations: [],
      tools: {
        history: []
      },
      files: {
        recent: [],
        modified: []
      },
      git: {
        currentBranch: null,
        recentCommits: []
      },
      environment: {
        cwd: process.cwd(),
        platform: os.platform(),
        hostname: os.hostname()
      },
      memory: {},
      timestamp: Date.now()
    };
  }
  
  /**
   * Update context with new information
   */
  public updateContext(updates: Record<string, any>): void {
    // Deep merge updates into context
    this.context = this.deepMerge(this.context, updates);
    
    // Trim context if it exceeds memory limit
    this.trimContextIfNeeded();
    
    // Update timestamp
    this.context.timestamp = Date.now();
    
    // Emit event for context updates
    this.emit('contextUpdated', this.context);
    
    // Log context update
    logger.debug('Context updated');
  }
  
  /**
   * Get specific context by key
   */
  public getContext(key?: string): any {
    if (!key) {
      return this.context;
    }
    
    // Support dot notation for nested keys (e.g., 'files.recent')
    const keys = key.split('.');
    let value = this.context;
    
    for (const k of keys) {
      if (value === undefined || value === null) {
        return undefined;
      }
      value = value[k];
    }
    
    return value;
  }
  
  /**
   * Get the full context
   */
  public getFullContext(): Record<string, any> {
    return this.context;
  }
  
  /**
   * Reset the context to initial state
   */
  public resetContext(): void {
    this.context = {
      conversations: [],
      tools: {
        history: []
      },
      files: {
        recent: [],
        modified: []
      },
      git: {
        currentBranch: null,
        recentCommits: []
      },
      environment: {
        cwd: process.cwd(),
        platform: os.platform(),
        hostname: os.hostname()
      },
      memory: {},
      timestamp: Date.now()
    };
    
    this.emit('contextReset');
    logger.info('Context reset to initial state');
  }
  
  /**
   * Persist context to disk
   */
  public async persistContext(): Promise<void> {
    try {
      // Ensure the directory exists
      await fs.mkdir(path.dirname(this.persistPath), { recursive: true });
      
      // Write context to file
      await fs.writeFile(
        this.persistPath,
        JSON.stringify(this.context, null, 2),
        'utf8'
      );
      
      logger.info(`Context persisted to ${this.persistPath}`);
    } catch (error) {
      logger.error('Error persisting context:', error);
      throw error;
    }
  }
  
  /**
   * Load persisted context from disk
   */
  public async loadPersistedContext(): Promise<void> {
    try {
      // Check if context file exists
      try {
        await fs.access(this.persistPath);
      } catch {
        logger.info('No persisted context found');
        return;
      }
      
      // Read and parse context file
      const data = await fs.readFile(this.persistPath, 'utf8');
      const loadedContext = JSON.parse(data);
      
      // Merge loaded context with current context
      this.context = this.deepMerge(this.context, loadedContext);
      
      // Update timestamp
      this.context.timestamp = Date.now();
      
      // Update environment information
      this.context.environment = {
        ...this.context.environment,
        cwd: process.cwd(),
        platform: os.platform(),
        hostname: os.hostname()
      };
      
      this.emit('contextLoaded', this.context);
      logger.info('Persisted context loaded');
    } catch (error) {
      logger.error('Error loading persisted context:', error);
      throw error;
    }
  }
  
  /**
   * Add a message to the conversation history
   */
  public addConversationMessage(role: 'user' | 'assistant' | 'system', content: string): void {
    // Ensure conversations array exists
    if (!this.context.conversations) {
      this.context.conversations = [];
    }
    
    // Add message to conversations
    this.context.conversations.push({
      role,
      content,
      timestamp: Date.now()
    });
    
    // Trim conversations if needed
    this.trimConversationsIfNeeded();
    
    this.emit('messageAdded', { role, content });
  }
  
  /**
   * Get conversation history
   */
  public getConversationHistory(limit?: number): any[] {
    if (!this.context.conversations) {
      return [];
    }
    
    const conversations = [...this.context.conversations];
    
    if (limit && limit > 0) {
      return conversations.slice(-limit);
    }
    
    return conversations;
  }
  
  /**
   * Add a file to recent files list
   */
  public addRecentFile(filePath: string): void {
    // Ensure files.recent array exists
    if (!this.context.files) {
      this.context.files = { recent: [], modified: [] };
    }
    if (!this.context.files.recent) {
      this.context.files.recent = [];
    }
    
    // Remove if already exists (to move to front)
    const index = this.context.files.recent.findIndex((f: string) => f === filePath);
    if (index !== -1) {
      this.context.files.recent.splice(index, 1);
    }
    
    // Add to front of the list
    this.context.files.recent.unshift(filePath);
    
    // Limit to 20 recent files
    if (this.context.files.recent.length > 20) {
      this.context.files.recent = this.context.files.recent.slice(0, 20);
    }
  }
  
  /**
   * Add a file to modified files list
   */
  public addModifiedFile(filePath: string, changeType: 'created' | 'modified' | 'deleted'): void {
    // Ensure files.modified array exists
    if (!this.context.files) {
      this.context.files = { recent: [], modified: [] };
    }
    if (!this.context.files.modified) {
      this.context.files.modified = [];
    }
    
    // Add to modified files
    this.context.files.modified.unshift({
      path: filePath,
      type: changeType,
      timestamp: Date.now()
    });
    
    // Limit to 50 modified files
    if (this.context.files.modified.length > 50) {
      this.context.files.modified = this.context.files.modified.slice(0, 50);
    }
  }
  
  /**
   * Update git context
   */
  public updateGitContext(gitInfo: any): void {
    this.context.git = { ...this.context.git, ...gitInfo };
  }
  
  /**
   * Store a value in memory
   */
  public memorize(key: string, value: any): void {
    if (!this.context.memory) {
      this.context.memory = {};
    }
    
    this.context.memory[key] = value;
  }
  
  /**
   * Retrieve a value from memory
   */
  public recall(key: string): any {
    if (!this.context.memory) {
      return undefined;
    }
    
    return this.context.memory[key];
  }
  
  /**
   * Add a tool execution to history
   */
  public addToolExecution(tool: string, params: any, result: any): void {
    // Ensure tools.history array exists
    if (!this.context.tools) {
      this.context.tools = { history: [] };
    }
    if (!this.context.tools.history) {
      this.context.tools.history = [];
    }
    
    // Add to tool history
    this.context.tools.history.unshift({
      tool,
      params,
      result: this.sanitizeToolResult(result),
      timestamp: Date.now()
    });
    
    // Limit to 50 tool executions
    if (this.context.tools.history.length > 50) {
      this.context.tools.history = this.context.tools.history.slice(0, 50);
    }
  }
  
  /**
   * Sanitize tool result to prevent excessive context growth
   */
  private sanitizeToolResult(result: any): any {
    if (!result) {
      return result;
    }
    
    // Clone the result to avoid modifying the original
    const sanitized = { ...result };
    
    // Truncate long strings
    for (const key in sanitized) {
      if (typeof sanitized[key] === 'string' && sanitized[key].length > 1000) {
        sanitized[key] = sanitized[key].substring(0, 1000) + '... [truncated]';
      } else if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
        sanitized[key] = this.sanitizeToolResult(sanitized[key]);
      }
    }
    
    return sanitized;
  }
  
  /**
   * Trim conversations if they get too large
   */
  private trimConversationsIfNeeded(): void {
    if (!this.context.conversations) {
      return;
    }
    
    // If we have more than 100 messages, trim to the last 50
    if (this.context.conversations.length > 100) {
      this.context.conversations = this.context.conversations.slice(-50);
    }
  }
  
  /**
   * Trim context if it exceeds memory limit
   */
  private trimContextIfNeeded(): void {
    const contextSize = this.getContextSize();
    
    if (contextSize > this.memoryLimit) {
      logger.warn(`Context size (${contextSize} bytes) exceeds limit (${this.memoryLimit} bytes). Trimming.`);
      
      // Trim conversations more aggressively
      if (this.context.conversations && this.context.conversations.length > 10) {
        this.context.conversations = this.context.conversations.slice(-10);
      }
      
      // Trim tool history
      if (this.context.tools && this.context.tools.history && this.context.tools.history.length > 10) {
        this.context.tools.history = this.context.tools.history.slice(-10);
      }
      
      // Trim file lists
      if (this.context.files) {
        if (this.context.files.recent && this.context.files.recent.length > 5) {
          this.context.files.recent = this.context.files.recent.slice(0, 5);
        }
        
        if (this.context.files.modified && this.context.files.modified.length > 10) {
          this.context.files.modified = this.context.files.modified.slice(0, 10);
        }
      }
      
      // Clear any memory items that are excessively large
      if (this.context.memory) {
        for (const key in this.context.memory) {
          const value = this.context.memory[key];
          if (typeof value === 'string' && value.length > 10000) {
            delete this.context.memory[key];
          }
        }
      }
    }
  }
  
  /**
   * Get approximate size of context in bytes
   */
  private getContextSize(): number {
    return Buffer.byteLength(JSON.stringify(this.context));
  }
  
  /**
   * Deep merge two objects
   */
  private deepMerge(target: any, source: any): any {
    // Handle case when source is not an object
    if (typeof source !== 'object' || source === null) {
      return source;
    }
    
    // Handle case when target is not an object
    if (typeof target !== 'object' || target === null) {
      return Array.isArray(source) ? [...source] : { ...source };
    }
    
    // Handle arrays: completely replace the array
    if (Array.isArray(source)) {
      return [...source];
    }
    
    // Both target and source are objects, merge them
    const output = { ...target };
    
    for (const key in source) {
      if (Object.prototype.hasOwnProperty.call(source, key)) {
        if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
          // Recursively merge objects
          output[key] = this.deepMerge(output[key] || {}, source[key]);
        } else {
          // Replace or add values
          output[key] = source[key];
        }
      }
    }
    
    return output;
  }
} 