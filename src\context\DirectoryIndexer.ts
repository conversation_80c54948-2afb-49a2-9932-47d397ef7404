import { EventEmitter } from 'events';
import path from 'path';
import fs from 'fs/promises';
import fastGlob from 'fast-glob';
import chokidar from 'chokidar';
import { logger } from '../utils/logger.js';
import { ContextManager } from './ContextManager.js';

/**
 * Interface for indexed file information
 */
export interface IndexedFile {
  path: string;
  relativePath: string;
  size: number;
  modified: Date;
  extension: string;
  type: 'file' | 'directory';
}

/**
 * Interface for project structure information
 */
export interface ProjectStructure {
  root: string;
  files: IndexedFile[];
  fileCount: number;
  directoryCount: number;
  totalSize: number;
  excludedPatterns: string[];
  extensions: Record<string, number>;
  timestamp: number;
}

/**
 * Directory Indexer class for automatically indexing and understanding project structure
 */
export class DirectoryIndexer extends EventEmitter {
  private contextManager: ContextManager;
  private watcher: chokidar.FSWatcher | null = null;
  private indexInProgress: boolean = false;
  private projectStructure: ProjectStructure;
  private excludePatterns: string[] = [
    '**/node_modules/**',
    '**/dist/**',
    '**/build/**',
    '**/.git/**',
    '**/coverage/**',
    '**/.DS_Store',
    '**/tmp/**',
    '**/*.log'
  ];
  
  constructor(contextManager: ContextManager) {
    super();
    this.contextManager = contextManager;
    
    // Initialize empty project structure
    this.projectStructure = {
      root: process.cwd(),
      files: [],
      fileCount: 0,
      directoryCount: 0,
      totalSize: 0,
      excludedPatterns: [...this.excludePatterns],
      extensions: {},
      timestamp: Date.now()
    };
  }
  
  /**
   * Index the current directory
   */
  public async indexCurrentDirectory(): Promise<ProjectStructure> {
    if (this.indexInProgress) {
      logger.warn('Indexing already in progress');
      return this.projectStructure;
    }
    
    this.indexInProgress = true;
    
    try {
      const cwd = process.cwd();
      logger.info(`Indexing directory: ${cwd}`);
      
      // Reset project structure
      this.projectStructure = {
        root: cwd,
        files: [],
        fileCount: 0,
        directoryCount: 0,
        totalSize: 0,
        excludedPatterns: [...this.excludePatterns],
        extensions: {},
        timestamp: Date.now()
      };
      
      // Find all files and directories, excluding patterns
      const entries = await fastGlob(['**/*'], {
        cwd,
        onlyFiles: false,
        dot: true,
        stats: true,
        ignore: this.excludePatterns,
        followSymbolicLinks: false
      });
      
      // Process each entry
      for (const entry of entries) {
        let filePath: string;
        let stats: fs.Stats;
        
        if (typeof entry === 'string') {
          filePath = path.join(cwd, entry);
          stats = await fs.stat(filePath);
        } else {
          filePath = entry.path;
          stats = entry.stats!;
        }
        
        const relativePath = path.relative(cwd, filePath);
        const extension = path.extname(filePath).toLowerCase();
        
        if (stats.isDirectory()) {
          this.projectStructure.directoryCount++;
          
          this.projectStructure.files.push({
            path: filePath,
            relativePath,
            size: 0,
            modified: stats.mtime,
            extension: '',
            type: 'directory'
          });
        } else {
          this.projectStructure.fileCount++;
          this.projectStructure.totalSize += stats.size;
          
          // Track file extensions
          if (extension) {
            this.projectStructure.extensions[extension] = 
              (this.projectStructure.extensions[extension] || 0) + 1;
          }
          
          this.projectStructure.files.push({
            path: filePath,
            relativePath,
            size: stats.size,
            modified: stats.mtime,
            extension,
            type: 'file'
          });
        }
      }
      
      // Update context with project structure
      this.contextManager.updateContext({
        projectStructure: {
          root: this.projectStructure.root,
          fileCount: this.projectStructure.fileCount,
          directoryCount: this.projectStructure.directoryCount,
          totalSize: this.projectStructure.totalSize,
          extensions: this.projectStructure.extensions,
          timestamp: this.projectStructure.timestamp
        }
      });
      
      logger.info(`Indexed ${this.projectStructure.fileCount} files and ${this.projectStructure.directoryCount} directories`);
      this.emit('indexed', this.projectStructure);
      
      // Start watching for changes
      this.startWatching();
      
      return this.projectStructure;
    } catch (error) {
      logger.error('Error indexing directory:', error);
      throw error;
    } finally {
      this.indexInProgress = false;
    }
  }
  
  /**
   * Refresh the directory index
   */
  public async refreshIndex(): Promise<ProjectStructure> {
    return this.indexCurrentDirectory();
  }
  
  /**
   * Start watching for file system changes
   */
  private startWatching(): void {
    if (this.watcher) {
      // Already watching, stop first
      this.stopWatching();
    }
    
    try {
      logger.info('Starting file system watcher');
      
      this.watcher = chokidar.watch('.', {
        ignored: this.excludePatterns,
        persistent: true,
        ignoreInitial: true,
        awaitWriteFinish: {
          stabilityThreshold: 300,
          pollInterval: 100
        }
      });
      
      // File added
      this.watcher.on('add', async (filePath) => {
        try {
          const stats = await fs.stat(filePath);
          const relativePath = path.relative(this.projectStructure.root, filePath);
          const extension = path.extname(filePath).toLowerCase();
          
          // Update project structure
          this.projectStructure.fileCount++;
          this.projectStructure.totalSize += stats.size;
          
          if (extension) {
            this.projectStructure.extensions[extension] = 
              (this.projectStructure.extensions[extension] || 0) + 1;
          }
          
          this.projectStructure.files.push({
            path: filePath,
            relativePath,
            size: stats.size,
            modified: stats.mtime,
            extension,
            type: 'file'
          });
          
          // Update context
          this.contextManager.addModifiedFile(filePath, 'created');
          
          // Emit event
          this.emit('fileAdded', { path: filePath, stats });
          this.emit('fileChanged', filePath);
        } catch (error) {
          logger.error(`Error processing added file ${filePath}:`, error);
        }
      });
      
      // File changed
      this.watcher.on('change', async (filePath) => {
        try {
          const stats = await fs.stat(filePath);
          const relativePath = path.relative(this.projectStructure.root, filePath);
          
          // Update file in project structure
          const fileIndex = this.projectStructure.files.findIndex(
            f => f.relativePath === relativePath
          );
          
          if (fileIndex !== -1) {
            const oldSize = this.projectStructure.files[fileIndex].size;
            this.projectStructure.totalSize = this.projectStructure.totalSize - oldSize + stats.size;
            
            this.projectStructure.files[fileIndex] = {
              ...this.projectStructure.files[fileIndex],
              size: stats.size,
              modified: stats.mtime
            };
          }
          
          // Update context
          this.contextManager.addModifiedFile(filePath, 'modified');
          this.contextManager.addRecentFile(filePath);
          
          // Emit event
          this.emit('fileChanged', filePath);
        } catch (error) {
          logger.error(`Error processing changed file ${filePath}:`, error);
        }
      });
      
      // File deleted
      this.watcher.on('unlink', (filePath) => {
        try {
          const relativePath = path.relative(this.projectStructure.root, filePath);
          
          // Remove file from project structure
          const fileIndex = this.projectStructure.files.findIndex(
            f => f.relativePath === relativePath
          );
          
          if (fileIndex !== -1) {
            const deletedFile = this.projectStructure.files[fileIndex];
            this.projectStructure.totalSize -= deletedFile.size;
            this.projectStructure.fileCount--;
            
            if (deletedFile.extension) {
              this.projectStructure.extensions[deletedFile.extension]--;
              if (this.projectStructure.extensions[deletedFile.extension] <= 0) {
                delete this.projectStructure.extensions[deletedFile.extension];
              }
            }
            
            this.projectStructure.files.splice(fileIndex, 1);
          }
          
          // Update context
          this.contextManager.addModifiedFile(filePath, 'deleted');
          
          // Emit event
          this.emit('fileDeleted', filePath);
          this.emit('fileChanged', filePath);
        } catch (error) {
          logger.error(`Error processing deleted file ${filePath}:`, error);
        }
      });
      
      // Directory added
      this.watcher.on('addDir', async (dirPath) => {
        try {
          const stats = await fs.stat(dirPath);
          const relativePath = path.relative(this.projectStructure.root, dirPath);
          
          // Update project structure
          this.projectStructure.directoryCount++;
          
          this.projectStructure.files.push({
            path: dirPath,
            relativePath,
            size: 0,
            modified: stats.mtime,
            extension: '',
            type: 'directory'
          });
          
          // Emit event
          this.emit('directoryAdded', { path: dirPath, stats });
        } catch (error) {
          logger.error(`Error processing added directory ${dirPath}:`, error);
        }
      });
      
      // Directory deleted
      this.watcher.on('unlinkDir', (dirPath) => {
        try {
          const relativePath = path.relative(this.projectStructure.root, dirPath);
          
          // Remove directory from project structure
          const dirIndex = this.projectStructure.files.findIndex(
            f => f.relativePath === relativePath && f.type === 'directory'
          );
          
          if (dirIndex !== -1) {
            this.projectStructure.directoryCount--;
            this.projectStructure.files.splice(dirIndex, 1);
          }
          
          // Emit event
          this.emit('directoryDeleted', dirPath);
        } catch (error) {
          logger.error(`Error processing deleted directory ${dirPath}:`, error);
        }
      });
      
      // Error in watcher
      this.watcher.on('error', (error) => {
        logger.error('Error in file system watcher:', error);
        this.emit('watcherError', error);
      });
      
      this.emit('watcherStarted');
    } catch (error) {
      logger.error('Error starting file system watcher:', error);
      throw error;
    }
  }
  
  /**
   * Stop watching for file system changes
   */
  public stopWatching(): void {
    if (this.watcher) {
      logger.info('Stopping file system watcher');
      this.watcher.close();
      this.watcher = null;
      this.emit('watcherStopped');
    }
  }
  
  /**
   * Set exclude patterns for indexing
   */
  public setExcludePatterns(patterns: string[]): void {
    this.excludePatterns = patterns;
    this.projectStructure.excludedPatterns = [...patterns];
    
    // Restart watcher with new patterns
    if (this.watcher) {
      this.stopWatching();
      this.startWatching();
    }
  }
  
  /**
   * Add an exclude pattern
   */
  public addExcludePattern(pattern: string): void {
    if (!this.excludePatterns.includes(pattern)) {
      this.excludePatterns.push(pattern);
      this.projectStructure.excludedPatterns.push(pattern);
      
      // Restart watcher with new patterns
      if (this.watcher) {
        this.stopWatching();
        this.startWatching();
      }
    }
  }
  
  /**
   * Get project structure
   */
  public getProjectStructure(): ProjectStructure {
    return this.projectStructure;
  }
  
  /**
   * Find files matching a pattern
   */
  public async findFiles(pattern: string): Promise<IndexedFile[]> {
    try {
      const matches = await fastGlob(pattern, {
        cwd: this.projectStructure.root,
        onlyFiles: true,
        ignore: this.excludePatterns,
        dot: true,
        stats: true
      });
      
      const result: IndexedFile[] = [];
      
      for (const entry of matches) {
        let filePath: string;
        let stats: fs.Stats;
        
        if (typeof entry === 'string') {
          filePath = path.join(this.projectStructure.root, entry);
          stats = await fs.stat(filePath);
        } else {
          filePath = entry.path;
          stats = entry.stats!;
        }
        
        const relativePath = path.relative(this.projectStructure.root, filePath);
        const extension = path.extname(filePath).toLowerCase();
        
        result.push({
          path: filePath,
          relativePath,
          size: stats.size,
          modified: stats.mtime,
          extension,
          type: 'file'
        });
      }
      
      return result;
    } catch (error) {
      logger.error(`Error finding files with pattern ${pattern}:`, error);
      throw error;
    }
  }
  
  /**
   * Clean up resources
   */
  public dispose(): void {
    this.stopWatching();
    this.removeAllListeners();
  }
} 