import { GitManager } from './GitManager.js';
import { DiffManager } from '../diff/DiffManager.js';
import { logger } from '../utils/logger.js';

let gitManager: GitManager | null = null;

/**
 * Setup git integration functionality
 */
export async function setupGitIntegration(diffManager?: DiffManager): Promise<GitManager> {
  try {
    logger.info('Setting up git integration');
    
    // Create diff manager if not provided
    const diffManagerInstance = diffManager || new DiffManager();
    
    // Create git manager
    gitManager = new GitManager(diffManagerInstance);
    
    // Initialize git functionality
    await gitManager.initialize();
    
    return gitManager;
  } catch (error) {
    logger.error('Error setting up git integration:', error);
    throw error;
  }
}

/**
 * Get the git manager instance
 */
export function getGitManager(): GitManager {
  if (!gitManager) {
    throw new Error('Git manager not initialized. Call setupGitIntegration first.');
  }
  
  return gitManager;
} 