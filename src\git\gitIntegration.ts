import { GitManager } from './GitManager.js';
import { DiffManager } from '../diff/DiffManager.js';
import { Config } from '../utils/config.js';
import { logger } from '../utils/logger.js';
import { isomorphicPath } from '../utils/isomorphicPath.js';
import path from 'path';
import fs from 'fs/promises';

let gitManager: GitManager | null = null;

/**
 * Setup git integration functionality
 */
export async function setupGitIntegration(
  config: Config,
  diffManager?: DiffManager,
  projectPath: string = process.cwd()
): Promise<GitManager> {
  try {
    logger.info('Setting up git integration');

    // Create diff manager if not provided
    const diffManagerInstance = diffManager || new DiffManager();

    // Create git manager
    gitManager = new GitManager(diffManagerInstance);

    // Initialize git functionality
    await gitManager.initialize();

    // Enhanced initialization
    await initializeGitIntegration(config, projectPath);

    return gitManager;
  } catch (error) {
    logger.error('Error setting up git integration:', error);
    throw error;
  }
}

/**
 * Get the git manager instance
 */
export function getGitManager(): GitManager {
  if (!gitManager) {
    throw new Error('Git manager not initialized. Call setupGitIntegration first.');
  }

  return gitManager;
}

/**
 * Initialize git integration for the project
 */
export async function initializeGitIntegration(
  config: Config,
  projectPath: string = process.cwd()
): Promise<void> {
  logger.info('Initializing enhanced git integration');

  try {
    if (!gitManager) {
      throw new Error('Git manager not initialized');
    }

    // Check if we're in a git repository
    const isGitRepo = await gitManager.isRepository(projectPath);

    if (!isGitRepo) {
      logger.warn(`Directory ${projectPath} is not a git repository`);

      // Ask if we should initialize a new repository
      if (config.git?.autoInit) {
        logger.info('Auto-initializing git repository');
        await gitManager.initRepository(projectPath);
        await setupInitialCommit(gitManager, projectPath);
      } else {
        logger.info('Git auto-initialization is disabled');
      }
    } else {
      logger.info('Git repository detected');
      await validateGitConfiguration(gitManager);
    }

    // Setup git hooks if enabled
    if (config.git?.enableHooks) {
      await setupGitHooks(gitManager, projectPath);
    }

    // Setup gitignore if it doesn't exist
    await ensureGitignore(projectPath);

    // Validate git configuration
    await validateGitSetup(gitManager);

    logger.info('Enhanced git integration initialized successfully');

  } catch (error) {
    logger.error('Failed to initialize enhanced git integration:', error);
    throw error;
  }
}

/**
 * Setup initial commit for a new repository
 */
async function setupInitialCommit(gitManager: GitManager, projectPath: string): Promise<void> {
  try {
    logger.info('Setting up initial commit');

    // Add all files
    await gitManager.addFiles(['.']).catch(() => {
      // Ignore errors if no files to add
    });

    // Create initial commit
    await gitManager.commit('Initial commit - AI Agent CLI Tool setup');

    logger.info('Initial commit created successfully');
  } catch (error) {
    logger.warn('Failed to create initial commit:', error);
  }
}

/**
 * Validate git configuration
 */
async function validateGitConfiguration(gitManager: GitManager): Promise<void> {
  try {
    logger.debug('Validating git configuration');

    // Check if user name and email are configured
    const userName = await gitManager.getConfig('user.name').catch(() => null);
    const userEmail = await gitManager.getConfig('user.email').catch(() => null);

    if (!userName || !userEmail) {
      logger.warn('Git user configuration is incomplete');
      logger.info('Please configure git user settings:');
      logger.info('  git config --global user.name "Your Name"');
      logger.info('  git config --global user.email "<EMAIL>"');
    } else {
      logger.debug(`Git user configured: ${userName} <${userEmail}>`);
    }

    // Check current branch
    const currentBranch = await gitManager.getCurrentBranch();
    logger.debug(`Current branch: ${currentBranch}`);

    // Check for uncommitted changes
    const status = await gitManager.getStatus();
    if (status.modified.length > 0 || status.created.length > 0 || status.deleted.length > 0) {
      logger.info(`Found ${status.modified.length + status.created.length + status.deleted.length} uncommitted changes`);
    }

  } catch (error) {
    logger.warn('Git configuration validation failed:', error);
  }
}

/**
 * Setup git hooks for enhanced functionality
 */
async function setupGitHooks(gitManager: GitManager, projectPath: string): Promise<void> {
  try {
    logger.info('Setting up git hooks');

    const hooksDir = isomorphicPath(path.join(projectPath, '.git', 'hooks'));

    // Ensure hooks directory exists
    await fs.mkdir(hooksDir, { recursive: true });

    // Pre-commit hook for code quality checks
    const preCommitHook = `#!/bin/sh
# AI Agent CLI Tool - Pre-commit hook
echo "🔍 Running pre-commit checks..."

# Check for TypeScript errors
if command -v tsc >/dev/null 2>&1; then
  echo "Checking TypeScript..."
  tsc --noEmit
  if [ $? -ne 0 ]; then
    echo "❌ TypeScript errors found. Please fix before committing."
    exit 1
  fi
fi

# Check for linting errors
if command -v eslint >/dev/null 2>&1; then
  echo "Running ESLint..."
  eslint . --ext .ts,.js
  if [ $? -ne 0 ]; then
    echo "❌ Linting errors found. Please fix before committing."
    exit 1
  fi
fi

echo "✅ Pre-commit checks passed!"
exit 0
`;

    const preCommitPath = isomorphicPath(path.join(hooksDir, 'pre-commit'));
    await fs.writeFile(preCommitPath, preCommitHook, { mode: 0o755 });

    // Post-commit hook for notifications
    const postCommitHook = `#!/bin/sh
# AI Agent CLI Tool - Post-commit hook
echo "✅ Commit successful: $(git log -1 --pretty=format:'%h - %s')"
`;

    const postCommitPath = isomorphicPath(path.join(hooksDir, 'post-commit'));
    await fs.writeFile(postCommitPath, postCommitHook, { mode: 0o755 });

    logger.info('Git hooks setup completed');

  } catch (error) {
    logger.warn('Failed to setup git hooks:', error);
  }
}