#!/usr/bin/env node
import { Command } from 'commander';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { AgentEngine } from './agent/AgentEngine.js';
import { setupProviders } from './ai/setupProviders.js';
import { setupConfig } from './utils/config.js';
import { displayBanner } from './ui/banner.js';
import { TerminalUI } from './ui/TerminalUI.js';
import { logger } from './utils/logger.js';
import { registerDefaultTools } from './tools/registerDefaultTools.js';
import { setupGitIntegration } from './git/gitIntegration.js';
import { setupSemantic } from './semantic/semanticSetup.js';
import { isomorphicPath } from './utils/isomorphicPath.js';

// Load environment variables
dotenv.config();

// Get the package version
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const packageJsonPath = isomorphicPath(path.join(__dirname, '..', 'package.json'));
const { version } = JSON.parse(await Bun.file(packageJsonPath).text());

async function main() {
  try {
    // Display welcome banner
    displayBanner(version);

    // Initialize the CLI
    const program = new Command();
    
    program
      .name('agent')
      .description('Autonomous AI-powered CLI tool for local development')
      .version(version);

    // Default command (interactive mode)
    program
      .argument('[input]', 'Optional command or question to process')
      .option('-v, --verbose', 'Enable verbose logging')
      .option('--model <model>', 'Specify the AI model to use')
      .option('--provider <provider>', 'Specify the AI provider (openai, deepseek, ollama)')
      .option('--config <path>', 'Path to custom configuration file')
      .action(async (input, options) => {
        try {
          // Initialize configuration
          const config = await setupConfig(options.config);
          
          // Setup providers
          const providers = await setupProviders(config, options.provider, options.model);
          
          // Register default tools
          const toolRegistry = registerDefaultTools();
          
          // Setup Git integration
          await setupGitIntegration();
          
          // Setup semantic search capabilities
          await setupSemantic(config);
          
          // Initialize the agent engine
          const agentEngine = new AgentEngine({
            providers,
            toolRegistry,
            config
          });
          
          // Initialize UI
          const ui = new TerminalUI(agentEngine);
          
          // Process input or start interactive mode
          if (input) {
            await agentEngine.process(input);
          } else {
            // Start interactive mode
            await ui.startInteractiveMode();
          }
        } catch (error) {
          logger.error('Error in agent execution:', error);
          process.exit(1);
        }
      });

    // Configuration command
    program
      .command('config')
      .description('Configure the agent settings')
      .action(async () => {
        try {
          await setupConfig(undefined, true);
          logger.info('Configuration updated successfully');
        } catch (error) {
          logger.error('Error updating configuration:', error);
          process.exit(1);
        }
      });

    // Parse command line arguments
    await program.parseAsync(process.argv);
  } catch (error) {
    logger.error('Fatal error:', error);
    process.exit(1);
  }
}

main().catch((error) => {
  logger.error('Unhandled error:', error);
  process.exit(1);
}); 