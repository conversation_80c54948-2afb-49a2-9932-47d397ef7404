import { SemanticSearchEngine } from './SemanticSearchEngine.js';
import { ContextManager } from '../context/ContextManager.js';
import { logger } from '../utils/logger.js';
import { Config } from '../utils/config.js';

let semanticSearchEngine: SemanticSearchEngine | null = null;

/**
 * Setup semantic search functionality
 */
export async function setupSemantic(config: Config, contextManager?: ContextManager): Promise<SemanticSearchEngine> {
  try {
    logger.info('Setting up semantic search functionality');
    
    // Create or use provided context manager
    const contextManagerInstance = contextManager || new ContextManager();
    
    // Create semantic search engine
    semanticSearchEngine = new SemanticSearchEngine(contextManagerInstance);
    
    // Initialize semantic search engine
    await semanticSearchEngine.initialize(config);
    
    return semanticSearchEngine;
  } catch (error) {
    logger.error('Error setting up semantic search:', error);
    throw error;
  }
}

/**
 * Get the semantic search engine instance
 */
export function getSemanticSearchEngine(): SemanticSearchEngine {
  if (!semanticSearchEngine) {
    throw new Error('Semantic search engine not initialized. Call setupSemantic first.');
  }
  
  return semanticSearchEngine;
} 