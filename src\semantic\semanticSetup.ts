import { SemanticSearchEngine } from './SemanticSearchEngine.js';
import { ContextManager } from '../context/ContextManager.js';
import { logger } from '../utils/logger.js';
import { Config } from '../utils/config.js';
import { isomorphicPath } from '../utils/isomorphicPath.js';
import path from 'path';
import fs from 'fs/promises';

let semanticSearchEngine: SemanticSearchEngine | null = null;

/**
 * Setup semantic search functionality
 */
export async function setupSemantic(
  config: Config,
  contextManager?: ContextManager,
  projectPath: string = process.cwd()
): Promise<SemanticSearchEngine> {
  try {
    logger.info('Setting up semantic search functionality');

    // Create or use provided context manager
    const contextManagerInstance = contextManager || new ContextManager();

    // Create semantic search engine
    semanticSearchEngine = new SemanticSearchEngine(contextManagerInstance);

    // Initialize semantic search engine
    await semanticSearchEngine.initialize(config);

    // Enhanced initialization
    await initializeSemanticSearch(config, projectPath);

    return semanticSearchEngine;
  } catch (error) {
    logger.error('Error setting up semantic search:', error);
    throw error;
  }
}

/**
 * Get the semantic search engine instance
 */
export function getSemanticSearchEngine(): SemanticSearchEngine {
  if (!semanticSearchEngine) {
    throw new Error('Semantic search engine not initialized. Call setupSemantic first.');
  }

  return semanticSearchEngine;
}

/**
 * Initialize semantic search engine for the project
 */
export async function initializeSemanticSearch(
  config: Config,
  projectPath: string = process.cwd()
): Promise<void> {
  logger.info('Initializing enhanced semantic search engine');

  try {
    if (!semanticSearchEngine) {
      throw new Error('Semantic search engine not initialized');
    }

    // Setup project indexing
    await setupProjectIndexing(semanticSearchEngine, projectPath, config);

    // Create semantic cache directory
    await createSemanticCacheDirectory(projectPath);

    // Index the project files
    await indexProjectFiles(semanticSearchEngine, projectPath, config);

    logger.info('Enhanced semantic search engine initialized successfully');

  } catch (error) {
    logger.error('Failed to initialize enhanced semantic search engine:', error);
    throw error;
  }
}

/**
 * Setup project indexing configuration
 */
async function setupProjectIndexing(
  semanticEngine: SemanticSearchEngine,
  projectPath: string,
  config: Config
): Promise<void> {
  try {
    logger.info('Setting up project indexing configuration');

    // Define file patterns to include
    const includePatterns = config.semantic?.includePatterns || [
      '**/*.ts',
      '**/*.js',
      '**/*.tsx',
      '**/*.jsx',
      '**/*.py',
      '**/*.java',
      '**/*.cpp',
      '**/*.c',
      '**/*.h',
      '**/*.cs',
      '**/*.go',
      '**/*.rs',
      '**/*.php',
      '**/*.rb',
      '**/*.swift',
      '**/*.kt',
      '**/*.scala',
      '**/*.md',
      '**/*.txt',
      '**/*.json',
      '**/*.yaml',
      '**/*.yml',
      '**/*.xml',
      '**/*.html',
      '**/*.css',
      '**/*.scss',
      '**/*.less',
      '**/*.sql'
    ];

    // Define file patterns to exclude
    const excludePatterns = config.semantic?.excludePatterns || [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/coverage/**',
      '**/.git/**',
      '**/.vscode/**',
      '**/.idea/**',
      '**/tmp/**',
      '**/temp/**',
      '**/*.log',
      '**/*.cache',
      '**/*.min.js',
      '**/*.min.css',
      '**/package-lock.json',
      '**/yarn.lock',
      '**/.DS_Store',
      '**/Thumbs.db'
    ];

    // Configure the semantic engine
    semanticEngine.setIncludePatterns(includePatterns);
    semanticEngine.setExcludePatterns(excludePatterns);

    // Set maximum file size for indexing (default: 1MB)
    const maxFileSize = config.semantic?.maxFileSize || 1024 * 1024;
    semanticEngine.setMaxFileSize(maxFileSize);

    // Set chunk size for large files (default: 1000 characters)
    const chunkSize = config.semantic?.chunkSize || 1000;
    semanticEngine.setChunkSize(chunkSize);

    logger.info('Project indexing configuration completed');

  } catch (error) {
    logger.error('Failed to setup project indexing:', error);
    throw error;
  }
}