import { ToolRegistry, Tool } from './ToolRegistry.js';
import { ToolCall } from '../ai/types.js';
import { logger } from '../utils/logger.js';

/**
 * Parsed tool call
 */
export interface ParsedToolCall {
  id: string;
  tool: Tool;
  args: Record<string, any>;
}

/**
 * Parses tool calls from AI responses
 */
export class ToolCallParser {
  private toolRegistry: ToolRegistry;

  constructor(toolRegistry: ToolRegistry) {
    this.toolRegistry = toolRegistry;
  }

  /**
   * Parse a tool call
   */
  public parse(toolCall: ToolCall): ParsedToolCall | null {
    try {
      // Get the tool from the registry
      const tool = this.toolRegistry.getTool(toolCall.name);
      
      if (!tool) {
        logger.warn(`Tool not found: ${toolCall.name}`);
        return null;
      }
      
      // Parse arguments
      let args: Record<string, any>;
      
      try {
        args = JSON.parse(toolCall.arguments);
      } catch (error) {
        logger.error(`Failed to parse tool arguments: ${toolCall.arguments}`, error);
        return null;
      }
      
      // Validate required parameters
      if (tool.parameters.required) {
        for (const requiredParam of tool.parameters.required) {
          if (args[requiredParam] === undefined) {
            logger.warn(`Missing required parameter: ${requiredParam} for tool ${toolCall.name}`);
            return null;
          }
        }
      }
      
      return {
        id: toolCall.id,
        tool,
        args
      };
    } catch (error) {
      logger.error('Error parsing tool call:', error);
      return null;
    }
  }

  /**
   * Parse multiple tool calls
   */
  public parseMultiple(toolCalls: ToolCall[]): ParsedToolCall[] {
    return toolCalls
      .map(toolCall => this.parse(toolCall))
      .filter((parsedToolCall): parsedToolCall is ParsedToolCall => parsedToolCall !== null);
  }
} 