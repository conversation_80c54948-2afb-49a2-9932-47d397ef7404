import { EventEmitter } from 'events';
import { ParsedToolCall } from './ToolCallParser.js';
import { logger } from '../utils/logger.js';

/**
 * Tool execution result
 */
export interface ToolExecutionResult {
  id: string;
  tool: string;
  success: boolean;
  output?: any;
  error?: Error;
  duration: number;
}

/**
 * Tool chain options
 */
export interface ToolChainOptions {
  parallel?: boolean;
  timeout?: number;
  maxConcurrent?: number;
}

/**
 * Executes tool calls
 */
export class ToolChainExecutor extends EventEmitter {
  private defaultTimeout: number = 60000; // 60 seconds
  private maxConcurrent: number = 5;

  constructor() {
    super();
  }

  /**
   * Execute a single tool call
   */
  public async executeToolCall(toolCall: ParsedToolCall): Promise<ToolExecutionResult> {
    const startTime = Date.now();
    
    try {
      logger.debug(`Executing tool: ${toolCall.tool.name}`);
      
      // Execute the tool handler
      const output = await toolCall.tool.handler(toolCall.args);
      
      const result: ToolExecutionResult = {
        id: toolCall.id,
        tool: toolCall.tool.name,
        success: true,
        output,
        duration: Date.now() - startTime
      };
      
      // Emit the result
      this.emit('toolExecuted', result);
      
      return result;
    } catch (error) {
      logger.error(`Error executing tool ${toolCall.tool.name}:`, error);
      
      const result: ToolExecutionResult = {
        id: toolCall.id,
        tool: toolCall.tool.name,
        success: false,
        error: error instanceof Error ? error : new Error(String(error)),
        duration: Date.now() - startTime
      };
      
      // Emit the result
      this.emit('toolExecuted', result);
      
      return result;
    }
  }

  /**
   * Execute multiple tool calls
   */
  public async executeToolChain(
    toolCalls: ParsedToolCall[],
    options: ToolChainOptions = {}
  ): Promise<ToolExecutionResult[]> {
    const {
      parallel = false,
      timeout = this.defaultTimeout,
      maxConcurrent = this.maxConcurrent
    } = options;
    
    // If parallel execution is requested, execute all tools concurrently
    if (parallel) {
      // Create batches to limit concurrency
      const results: ToolExecutionResult[] = [];
      const batches: ParsedToolCall[][] = [];
      
      // Create batches of tool calls
      for (let i = 0; i < toolCalls.length; i += maxConcurrent) {
        batches.push(toolCalls.slice(i, i + maxConcurrent));
      }
      
      // Execute each batch in parallel
      for (const batch of batches) {
        const batchPromises = batch.map(toolCall => {
          return this.executeWithTimeout(toolCall, timeout);
        });
        
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
      }
      
      return results;
    } else {
      // Sequential execution
      const results: ToolExecutionResult[] = [];
      
      for (const toolCall of toolCalls) {
        const result = await this.executeWithTimeout(toolCall, timeout);
        results.push(result);
        
        // If a tool fails, stop the chain
        if (!result.success) {
          logger.warn(`Tool chain execution stopped due to failure in ${toolCall.tool.name}`);
          break;
        }
      }
      
      return results;
    }
  }

  /**
   * Execute a tool call with a timeout
   */
  private async executeWithTimeout(
    toolCall: ParsedToolCall,
    timeout: number
  ): Promise<ToolExecutionResult> {
    return new Promise<ToolExecutionResult>(resolve => {
      // Create a timeout
      const timeoutId = setTimeout(() => {
        resolve({
          id: toolCall.id,
          tool: toolCall.tool.name,
          success: false,
          error: new Error(`Tool execution timed out after ${timeout}ms`),
          duration: timeout
        });
      }, timeout);
      
      // Execute the tool
      this.executeToolCall(toolCall).then(result => {
        clearTimeout(timeoutId);
        resolve(result);
      });
    });
  }
} 