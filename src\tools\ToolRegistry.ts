import { logger } from '../utils/logger.js';
import { z } from 'zod';

/**
 * Tool parameter schema
 */
export interface ToolParameter {
  name: string;
  type: string;
  description: string;
  required?: boolean;
}

/**
 * Tool schema
 */
export interface Tool {
  name: string;
  description: string;
  parameters: {
    type: string;
    properties: Record<string, ToolParameter>;
    required?: string[];
  };
  handler: (args: Record<string, any>) => Promise<any>;
}

/**
 * Registry for all available tools
 */
export class ToolRegistry {
  private tools: Map<string, Tool> = new Map();
  
  /**
   * Register a new tool
   */
  public registerTool(tool: Tool): void {
    if (this.tools.has(tool.name)) {
      logger.warn(`Tool with name ${tool.name} already exists. Overwriting.`);
    }
    
    this.tools.set(tool.name, tool);
    logger.debug(`Registered tool: ${tool.name}`);
  }
  
  /**
   * Register multiple tools at once
   */
  public registerTools(tools: Tool[]): void {
    tools.forEach(tool => this.registerTool(tool));
  }
  
  /**
   * Get a tool by name
   */
  public getTool(name: string): Tool | undefined {
    return this.tools.get(name);
  }
  
  /**
   * Get all registered tools
   */
  public getAllTools(): Tool[] {
    return Array.from(this.tools.values());
  }
  
  /**
   * Get tool names by category
   */
  public getToolsByCategory(category: string): Tool[] {
    return this.getAllTools().filter(tool => tool.name.startsWith(`${category}.`));
  }
  
  /**
   * Check if a tool exists
   */
  public hasTool(name: string): boolean {
    return this.tools.has(name);
  }
  
  /**
   * Remove a tool
   */
  public removeTool(name: string): boolean {
    const result = this.tools.delete(name);
    
    if (result) {
      logger.debug(`Removed tool: ${name}`);
    }
    
    return result;
  }
  
  /**
   * Get the number of registered tools
   */
  public getToolCount(): number {
    return this.tools.size;
  }
  
  /**
   * Get tool definitions in OpenAI format for AI provider communication
   */
  public getToolDefinitionsForAI(): any[] {
    return Array.from(this.tools.values()).map(tool => {
      const parameters = this.convertZodSchemaToJsonSchema(tool.parameters);
      
      return {
        type: 'function',
        function: {
          name: tool.name,
          description: tool.description,
          parameters
        }
      };
    });
  }
  
  /**
   * Convert Zod schema to JSON schema for OpenAI API
   */
  private convertZodSchemaToJsonSchema(schema: z.ZodObject<any>): any {
    const jsonSchema: any = {
      type: 'object',
      properties: {},
      required: []
    };
    
    // Get the shape of the Zod schema
    const shape = schema._def.shape();
    
    // Convert each property in the shape to JSON schema
    Object.entries(shape).forEach(([key, value]: [string, any]) => {
      if (value instanceof z.ZodString) {
        jsonSchema.properties[key] = { type: 'string' };
        
        // Add description if available
        if (value.description) {
          jsonSchema.properties[key].description = value.description;
        }
      } else if (value instanceof z.ZodNumber) {
        jsonSchema.properties[key] = { type: 'number' };
        
        // Add description if available
        if (value.description) {
          jsonSchema.properties[key].description = value.description;
        }
      } else if (value instanceof z.ZodBoolean) {
        jsonSchema.properties[key] = { type: 'boolean' };
        
        // Add description if available
        if (value.description) {
          jsonSchema.properties[key].description = value.description;
        }
      } else if (value instanceof z.ZodArray) {
        jsonSchema.properties[key] = { 
          type: 'array',
          items: this.getZodTypeSchema(value._def.type)
        };
        
        // Add description if available
        if (value.description) {
          jsonSchema.properties[key].description = value.description;
        }
      } else if (value instanceof z.ZodObject) {
        jsonSchema.properties[key] = this.convertZodSchemaToJsonSchema(value);
        
        // Add description if available
        if (value.description) {
          jsonSchema.properties[key].description = value.description;
        }
      } else if (value instanceof z.ZodEnum) {
        jsonSchema.properties[key] = { 
          type: 'string',
          enum: value._def.values
        };
        
        // Add description if available
        if (value.description) {
          jsonSchema.properties[key].description = value.description;
        }
      } else {
        // Default to string for unknown types
        jsonSchema.properties[key] = { type: 'string' };
      }
      
      // Check if the property is required
      if (!value.isOptional()) {
        jsonSchema.required.push(key);
      }
    });
    
    return jsonSchema;
  }
  
  /**
   * Helper function to get JSON schema for a Zod type
   */
  private getZodTypeSchema(zodType: any): any {
    if (zodType instanceof z.ZodString) {
      return { type: 'string' };
    } else if (zodType instanceof z.ZodNumber) {
      return { type: 'number' };
    } else if (zodType instanceof z.ZodBoolean) {
      return { type: 'boolean' };
    } else if (zodType instanceof z.ZodObject) {
      return this.convertZodSchemaToJsonSchema(zodType);
    } else {
      // Default to string for unknown types
      return { type: 'string' };
    }
  }
  
  /**
   * Validate tool parameters against schema
   */
  public validateToolParameters(toolName: string, params: any): any {
    const tool = this.getTool(toolName);
    
    if (!tool) {
      throw new Error(`Tool '${toolName}' not found in registry`);
    }
    
    try {
      // Use Zod to validate and parse parameters
      return tool.parameters.parse(params);
    } catch (error) {
      if (error instanceof z.ZodError) {
        const formattedError = error.format();
        logger.error(`Tool parameter validation failed for '${toolName}':`, formattedError);
        throw new Error(`Invalid parameters for tool '${toolName}': ${JSON.stringify(formattedError)}`);
      }
      throw error;
    }
  }
  
  /**
   * Execute a tool with validated parameters
   */
  public async executeTool(toolName: string, params: any): Promise<any> {
    const tool = this.getTool(toolName);
    
    if (!tool) {
      throw new Error(`Tool '${toolName}' not found in registry`);
    }
    
    try {
      // Validate parameters
      const validatedParams = this.validateToolParameters(toolName, params);
      
      // Execute the tool
      return await tool.handler(validatedParams);
    } catch (error) {
      logger.error(`Error executing tool '${toolName}':`, error);
      throw error;
    }
  }
} 