import { z } from 'zod';
import { ToolRegistry } from './ToolRegistry.js';
import { AgentEngine } from '../agent/AgentEngine.js';
import { DiffManager } from '../diff/DiffManager.js';
import { logger } from '../utils/logger.js';

// Reference to agent engine for accessing diff manager
let agentEngine: AgentEngine | null = null;

/**
 * Set the agent engine reference for diff tools
 */
export function setAgentEngineForDiffTools(engine: AgentEngine): void {
  agentEngine = engine;
}

/**
 * Get diff manager safely
 */
function getDiffManager(): DiffManager {
  if (!agentEngine) {
    throw new Error('Agent engine not set for diff tools');
  }
  
  return agentEngine.getDiffManager();
}

/**
 * Register all diff-related tools
 */
export function registerDiffTools(registry: ToolRegistry): void {
  logger.info('Registering diff tools');
  
  // Track File Change Tool
  registry.registerTool({
    name: 'diff_track_file',
    description: 'Track a file for changes',
    parameters: z.object({
      file_path: z.string().describe('Path to the file to track')
    }),
    execute: async (params: { file_path: string }) => {
      const diffManager = getDiffManager();
      await diffManager.trackFileChange(params.file_path);
      return { success: true, message: `Now tracking changes for ${params.file_path}` };
    }
  });
  
  // Generate File Diff Tool
  registry.registerTool({
    name: 'diff_generate',
    description: 'Generate a diff for a tracked file',
    parameters: z.object({
      file_path: z.string().describe('Path to the file to generate diff for')
    }),
    execute: async (params: { file_path: string }) => {
      const diffManager = getDiffManager();
      const diff = await diffManager.generateFileDiff(params.file_path);
      
      if (!diff) {
        return { success: false, message: `Failed to generate diff for ${params.file_path}` };
      }
      
      return { 
        success: true, 
        diff: {
          filePath: diff.filePath,
          summary: diff.summary,
          hunks: diff.hunks,
          timestamp: diff.timestamp
        },
        formatted: diffManager.formatDiffForTerminal(diff)
      };
    }
  });
  
  // Compare Files Tool
  registry.registerTool({
    name: 'diff_compare_files',
    description: 'Compare two files and generate a diff',
    parameters: z.object({
      file_path1: z.string().describe('Path to the first file'),
      file_path2: z.string().describe('Path to the second file')
    }),
    execute: async (params: { file_path1: string; file_path2: string }) => {
      const diffManager = getDiffManager();
      const diff = await diffManager.compareFiles(params.file_path1, params.file_path2);
      
      if (!diff) {
        return { success: false, message: `Failed to compare files ${params.file_path1} and ${params.file_path2}` };
      }
      
      return { 
        success: true, 
        diff: {
          filePath: diff.filePath,
          summary: diff.summary,
          hunks: diff.hunks,
          timestamp: diff.timestamp
        },
        formatted: diffManager.formatDiffForTerminal(diff)
      };
    }
  });
  
  // Apply Patch Tool
  registry.registerTool({
    name: 'diff_apply_patch',
    description: 'Apply a patch to a file',
    parameters: z.object({
      file_path: z.string().describe('Path to the file to apply patch to'),
      hunks: z.array(z.object({
        oldStart: z.number(),
        oldLines: z.number(),
        newStart: z.number(),
        newLines: z.number(),
        lines: z.array(z.string())
      })).describe('Hunks to apply')
    }),
    execute: async (params: { file_path: string; hunks: any[] }) => {
      // Create a simple diff object from the provided hunks
      const diffManager = getDiffManager();
      
      // Track the file first
      await diffManager.trackFileChange(params.file_path);
      
      // Generate the current diff
      const currentDiff = await diffManager.generateFileDiff(params.file_path);
      
      if (!currentDiff) {
        return { success: false, message: `Failed to generate diff for ${params.file_path}` };
      }
      
      // Create a new diff with the provided hunks
      const patchDiff = {
        ...currentDiff,
        hunks: params.hunks
      };
      
      // Apply the patch
      const success = await diffManager.applyDiffToFile(params.file_path, patchDiff);
      
      return { 
        success, 
        message: success ? `Patch applied to ${params.file_path}` : `Failed to apply patch to ${params.file_path}`
      };
    }
  });
  
  // Get All Diffs Tool
  registry.registerTool({
    name: 'diff_get_all',
    description: 'Get all tracked diffs',
    parameters: z.object({}),
    execute: async () => {
      const diffManager = getDiffManager();
      const diffs = diffManager.getAllDiffs();
      
      return { 
        success: true,
        diffs: diffs.map(diff => ({
          filePath: diff.filePath,
          summary: diff.summary,
          timestamp: diff.timestamp
        }))
      };
    }
  });
  
  // Get Tracked Files Tool
  registry.registerTool({
    name: 'diff_get_tracked_files',
    description: 'Get all tracked files',
    parameters: z.object({}),
    execute: async () => {
      const diffManager = getDiffManager();
      const trackedFiles = diffManager.getTrackedFiles();
      
      return { 
        success: true,
        trackedFiles
      };
    }
  });
  
  // Untrack File Tool
  registry.registerTool({
    name: 'diff_untrack_file',
    description: 'Stop tracking a file for changes',
    parameters: z.object({
      file_path: z.string().describe('Path to the file to stop tracking')
    }),
    execute: async (params: { file_path: string }) => {
      const diffManager = getDiffManager();
      const success = diffManager.untrackFile(params.file_path);
      
      return { 
        success, 
        message: success ? `Stopped tracking ${params.file_path}` : `File ${params.file_path} was not being tracked`
      };
    }
  });
  
  // Reset Tracking Tool
  registry.registerTool({
    name: 'diff_reset_tracking',
    description: 'Reset all file tracking',
    parameters: z.object({}),
    execute: async () => {
      const diffManager = getDiffManager();
      diffManager.resetTracking();
      
      return { 
        success: true,
        message: 'Reset all file tracking'
      };
    }
  });
  
  logger.info('Diff tools registered successfully');
} 