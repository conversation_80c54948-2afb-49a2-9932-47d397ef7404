import { ToolRegistry } from '../ToolRegistry.js';
import { logger } from '../../utils/logger.js';
import { isomorphicPath } from '../../utils/isomorphicPath.js';
import fs from 'fs/promises';
import path from 'path';

/**
 * Register all file operation tools
 */
export function registerFileTools(registry: ToolRegistry): void {
  logger.info('Registering file operation tools');

  // Read file tool
  registry.registerTool({
    name: 'read_file',
    description: 'Read the contents of a file',
    parameters: {
      type: 'object',
      properties: {
        path: {
          type: 'string',
          description: 'Path to the file to read'
        },
        encoding: {
          type: 'string',
          description: 'File encoding (default: utf-8)',
          default: 'utf-8'
        }
      },
      required: ['path']
    },
    execute: async (args: any) => {
      try {
        const filePath = isomorphicPath(args.path);
        const encoding = args.encoding || 'utf-8';
        
        logger.debug(`Reading file: ${filePath}`);
        const content = await fs.readFile(filePath, encoding);
        
        return {
          success: true,
          content,
          path: filePath,
          size: content.length
        };
      } catch (error) {
        logger.error(`Failed to read file ${args.path}:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
  });

  // Write file tool
  registry.registerTool({
    name: 'write_file',
    description: 'Write content to a file',
    parameters: {
      type: 'object',
      properties: {
        path: {
          type: 'string',
          description: 'Path to the file to write'
        },
        content: {
          type: 'string',
          description: 'Content to write to the file'
        },
        encoding: {
          type: 'string',
          description: 'File encoding (default: utf-8)',
          default: 'utf-8'
        },
        createDirectories: {
          type: 'boolean',
          description: 'Create parent directories if they don\'t exist',
          default: true
        }
      },
      required: ['path', 'content']
    },
    execute: async (args: any) => {
      try {
        const filePath = isomorphicPath(args.path);
        const content = args.content;
        const encoding = args.encoding || 'utf-8';
        
        // Create parent directories if needed
        if (args.createDirectories !== false) {
          const dir = path.dirname(filePath);
          await fs.mkdir(dir, { recursive: true });
        }
        
        logger.debug(`Writing file: ${filePath}`);
        await fs.writeFile(filePath, content, encoding);
        
        return {
          success: true,
          path: filePath,
          size: content.length
        };
      } catch (error) {
        logger.error(`Failed to write file ${args.path}:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
  });

  // Copy file tool
  registry.registerTool({
    name: 'copy_file',
    description: 'Copy a file from source to destination',
    parameters: {
      type: 'object',
      properties: {
        source: {
          type: 'string',
          description: 'Source file path'
        },
        destination: {
          type: 'string',
          description: 'Destination file path'
        },
        overwrite: {
          type: 'boolean',
          description: 'Overwrite destination if it exists',
          default: false
        }
      },
      required: ['source', 'destination']
    },
    execute: async (args: any) => {
      try {
        const sourcePath = isomorphicPath(args.source);
        const destPath = isomorphicPath(args.destination);
        
        // Check if destination exists and overwrite is false
        if (!args.overwrite) {
          try {
            await fs.access(destPath);
            return {
              success: false,
              error: 'Destination file exists and overwrite is false'
            };
          } catch {
            // File doesn't exist, continue
          }
        }
        
        // Create destination directory if needed
        const destDir = path.dirname(destPath);
        await fs.mkdir(destDir, { recursive: true });
        
        logger.debug(`Copying file: ${sourcePath} -> ${destPath}`);
        await fs.copyFile(sourcePath, destPath);
        
        return {
          success: true,
          source: sourcePath,
          destination: destPath
        };
      } catch (error) {
        logger.error(`Failed to copy file ${args.source} to ${args.destination}:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
  });

  // Move file tool
  registry.registerTool({
    name: 'move_file',
    description: 'Move a file from source to destination',
    parameters: {
      type: 'object',
      properties: {
        source: {
          type: 'string',
          description: 'Source file path'
        },
        destination: {
          type: 'string',
          description: 'Destination file path'
        },
        overwrite: {
          type: 'boolean',
          description: 'Overwrite destination if it exists',
          default: false
        }
      },
      required: ['source', 'destination']
    },
    execute: async (args: any) => {
      try {
        const sourcePath = isomorphicPath(args.source);
        const destPath = isomorphicPath(args.destination);
        
        // Check if destination exists and overwrite is false
        if (!args.overwrite) {
          try {
            await fs.access(destPath);
            return {
              success: false,
              error: 'Destination file exists and overwrite is false'
            };
          } catch {
            // File doesn't exist, continue
          }
        }
        
        // Create destination directory if needed
        const destDir = path.dirname(destPath);
        await fs.mkdir(destDir, { recursive: true });
        
        logger.debug(`Moving file: ${sourcePath} -> ${destPath}`);
        await fs.rename(sourcePath, destPath);
        
        return {
          success: true,
          source: sourcePath,
          destination: destPath
        };
      } catch (error) {
        logger.error(`Failed to move file ${args.source} to ${args.destination}:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
  });

  // Delete file tool
  registry.registerTool({
    name: 'delete_file',
    description: 'Delete a file',
    parameters: {
      type: 'object',
      properties: {
        path: {
          type: 'string',
          description: 'Path to the file to delete'
        },
        force: {
          type: 'boolean',
          description: 'Force deletion even if file is read-only',
          default: false
        }
      },
      required: ['path']
    },
    execute: async (args: any) => {
      try {
        const filePath = isomorphicPath(args.path);
        
        logger.debug(`Deleting file: ${filePath}`);
        await fs.unlink(filePath);
        
        return {
          success: true,
          path: filePath
        };
      } catch (error) {
        logger.error(`Failed to delete file ${args.path}:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
  });

  // List directory tool
  registry.registerTool({
    name: 'list_directory',
    description: 'List contents of a directory',
    parameters: {
      type: 'object',
      properties: {
        path: {
          type: 'string',
          description: 'Path to the directory to list'
        },
        recursive: {
          type: 'boolean',
          description: 'List recursively',
          default: false
        },
        includeHidden: {
          type: 'boolean',
          description: 'Include hidden files and directories',
          default: false
        }
      },
      required: ['path']
    },
    execute: async (args: any) => {
      try {
        const dirPath = isomorphicPath(args.path);
        
        logger.debug(`Listing directory: ${dirPath}`);
        
        if (args.recursive) {
          return await listDirectoryRecursive(dirPath, args.includeHidden);
        } else {
          return await listDirectoryFlat(dirPath, args.includeHidden);
        }
      } catch (error) {
        logger.error(`Failed to list directory ${args.path}:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
  });

  logger.info('File operation tools registered successfully');
}

/**
 * List directory contents (flat)
 */
async function listDirectoryFlat(dirPath: string, includeHidden: boolean): Promise<any> {
  const entries = await fs.readdir(dirPath, { withFileTypes: true });
  const items = [];
  
  for (const entry of entries) {
    if (!includeHidden && entry.name.startsWith('.')) {
      continue;
    }
    
    const fullPath = isomorphicPath(path.join(dirPath, entry.name));
    const stats = await fs.stat(fullPath);
    
    items.push({
      name: entry.name,
      path: fullPath,
      type: entry.isDirectory() ? 'directory' : 'file',
      size: entry.isFile() ? stats.size : undefined,
      modified: stats.mtime.toISOString()
    });
  }
  
  return {
    success: true,
    path: dirPath,
    items
  };
}

/**
 * List directory contents (recursive)
 */
async function listDirectoryRecursive(dirPath: string, includeHidden: boolean): Promise<any> {
  const items: any[] = [];
  
  async function walkDirectory(currentPath: string, relativePath: string = ''): Promise<void> {
    const entries = await fs.readdir(currentPath, { withFileTypes: true });
    
    for (const entry of entries) {
      if (!includeHidden && entry.name.startsWith('.')) {
        continue;
      }
      
      const fullPath = isomorphicPath(path.join(currentPath, entry.name));
      const relPath = relativePath ? path.join(relativePath, entry.name) : entry.name;
      const stats = await fs.stat(fullPath);
      
      items.push({
        name: entry.name,
        path: fullPath,
        relativePath: relPath,
        type: entry.isDirectory() ? 'directory' : 'file',
        size: entry.isFile() ? stats.size : undefined,
        modified: stats.mtime.toISOString()
      });
      
      if (entry.isDirectory()) {
        await walkDirectory(fullPath, relPath);
      }
    }
  }
  
  await walkDirectory(dirPath);
  
  return {
    success: true,
    path: dirPath,
    items
  };
}
