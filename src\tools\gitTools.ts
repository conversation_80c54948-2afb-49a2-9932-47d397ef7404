import { z } from 'zod';
import { ToolRegistry } from './ToolRegistry.js';
import { getGitManager } from '../git/gitIntegration.js';
import { GitOperationType } from '../git/GitManager.js';
import { logger } from '../utils/logger.js';

/**
 * Register all git-related tools
 */
export function registerGitTools(registry: ToolRegistry): void {
  logger.info('Registering git tools');
  
  // Git Status Tool
  registry.registerTool({
    name: 'git_status',
    description: 'Get the current status of the git repository',
    parameters: z.object({}),
    execute: async () => {
      const gitManager = getGitManager();
      const result = await gitManager.getStatus();
      return result;
    }
  });
  
  // Git Branch Tool
  registry.registerTool({
    name: 'git_branch',
    description: 'Create a new git branch and switch to it',
    parameters: z.object({
      branch_name: z.string().describe('Name of the branch to create')
    }),
    execute: async (params: { branch_name: string }) => {
      const gitManager = getGitManager();
      const result = await gitManager.createBranch(params.branch_name);
      return result;
    }
  });
  
  // Git Checkout Tool
  registry.registerTool({
    name: 'git_checkout',
    description: 'Switch to an existing git branch',
    parameters: z.object({
      branch_name: z.string().describe('Name of the branch to checkout')
    }),
    execute: async (params: { branch_name: string }) => {
      const gitManager = getGitManager();
      const result = await gitManager.checkoutBranch(params.branch_name);
      return result;
    }
  });
  
  // Git Commit Tool
  registry.registerTool({
    name: 'git_commit',
    description: 'Commit changes to the git repository',
    parameters: z.object({
      message: z.string().describe('Commit message'),
      files: z.array(z.string()).optional().describe('Optional list of files to commit. If not provided, all changes will be committed.')
    }),
    execute: async (params: { message: string; files?: string[] }) => {
      const gitManager = getGitManager();
      const result = await gitManager.commitChanges(params.message, params.files);
      return result;
    }
  });
  
  // Git Log Tool
  registry.registerTool({
    name: 'git_log',
    description: 'Get commit history from the git repository',
    parameters: z.object({
      max_count: z.number().optional().describe('Maximum number of commits to retrieve')
    }),
    execute: async (params: { max_count?: number }) => {
      const gitManager = getGitManager();
      const result = await gitManager.getCommitHistory(params.max_count || 10);
      return result;
    }
  });
  
  // Git Pull Tool
  registry.registerTool({
    name: 'git_pull',
    description: 'Pull changes from a remote git repository',
    parameters: z.object({
      remote: z.string().optional().describe('Remote name (default: origin)'),
      branch: z.string().optional().describe('Branch name to pull from')
    }),
    execute: async (params: { remote?: string; branch?: string }) => {
      const gitManager = getGitManager();
      const result = await gitManager.pullChanges(params.remote, params.branch);
      return result;
    }
  });
  
  // Git Push Tool
  registry.registerTool({
    name: 'git_push',
    description: 'Push changes to a remote git repository',
    parameters: z.object({
      remote: z.string().optional().describe('Remote name (default: origin)'),
      branch: z.string().optional().describe('Branch name to push to')
    }),
    execute: async (params: { remote?: string; branch?: string }) => {
      const gitManager = getGitManager();
      const result = await gitManager.pushChanges(params.remote, params.branch);
      return result;
    }
  });
  
  // Git Diff Tool
  registry.registerTool({
    name: 'git_diff',
    description: 'Get diff between commits or files',
    parameters: z.object({
      commit1: z.string().optional().describe('First commit hash'),
      commit2: z.string().optional().describe('Second commit hash')
    }),
    execute: async (params: { commit1?: string; commit2?: string }) => {
      const gitManager = getGitManager();
      const result = await gitManager.getDiff(params.commit1, params.commit2);
      return result;
    }
  });
  
  // Git Merge Tool
  registry.registerTool({
    name: 'git_merge',
    description: 'Merge a branch into the current branch',
    parameters: z.object({
      branch_name: z.string().describe('Name of the branch to merge into current branch')
    }),
    execute: async (params: { branch_name: string }) => {
      const gitManager = getGitManager();
      const result = await gitManager.mergeBranch(params.branch_name);
      return result;
    }
  });
  
  // Git Repository Info Tool
  registry.registerTool({
    name: 'git_repo_info',
    description: 'Get information about the current git repository',
    parameters: z.object({}),
    execute: async () => {
      const gitManager = getGitManager();
      return gitManager.getRepositoryInfo();
    }
  });
  
  // Git Visual History Tool
  registry.registerTool({
    name: 'git_visual_history',
    description: 'Get a visual representation of git history',
    parameters: z.object({
      max_count: z.number().optional().describe('Maximum number of commits to show')
    }),
    execute: async (params: { max_count?: number }) => {
      const gitManager = getGitManager();
      const history = await gitManager.getVisualHistory(params.max_count || 20);
      return { visualHistory: history };
    }
  });
  
  logger.info('Git tools registered successfully');
} 