import { ToolRegistry } from '../ToolRegistry.js';
import { logger } from '../../utils/logger.js';
import { GitManager } from '../../git/GitManager.js';
import { DiffManager } from '../../diff/DiffManager.js';

let gitManager: GitManager | null = null;

/**
 * Initialize git manager for tools
 */
function getGitManager(): GitManager {
  if (!gitManager) {
    gitManager = new GitManager(new DiffManager());
  }
  return gitManager;
}

/**
 * Register all git operation tools
 */
export function registerGitTools(registry: ToolRegistry): void {
  logger.info('Registering git operation tools');

  // Git status tool
  registry.registerTool({
    name: 'git_status',
    description: 'Get git repository status',
    parameters: {
      type: 'object',
      properties: {
        path: {
          type: 'string',
          description: 'Repository path (default: current directory)',
          default: process.cwd()
        }
      }
    },
    execute: async (args: any) => {
      try {
        const git = getGitManager();
        const repoPath = args.path || process.cwd();
        
        logger.debug(`Getting git status for: ${repoPath}`);
        
        const status = await git.getStatus();
        const currentBranch = await git.getCurrentBranch();
        const isRepo = await git.isRepository(repoPath);
        
        return {
          success: true,
          isRepository: isRepo,
          currentBranch,
          status: {
            modified: status.modified,
            created: status.created,
            deleted: status.deleted,
            renamed: status.renamed,
            staged: status.staged
          }
        };
      } catch (error) {
        logger.error('Failed to get git status:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
  });

  // Git add tool
  registry.registerTool({
    name: 'git_add',
    description: 'Add files to git staging area',
    parameters: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: { type: 'string' },
          description: 'Files to add (use "." for all files)'
        }
      },
      required: ['files']
    },
    execute: async (args: any) => {
      try {
        const git = getGitManager();
        const files = args.files;
        
        logger.debug(`Adding files to git: ${files.join(', ')}`);
        
        await git.addFiles(files);
        
        return {
          success: true,
          files
        };
      } catch (error) {
        logger.error('Failed to add files to git:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
  });

  // Git commit tool
  registry.registerTool({
    name: 'git_commit',
    description: 'Commit staged changes',
    parameters: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          description: 'Commit message'
        },
        addAll: {
          type: 'boolean',
          description: 'Add all modified files before committing',
          default: false
        }
      },
      required: ['message']
    },
    execute: async (args: any) => {
      try {
        const git = getGitManager();
        const message = args.message;
        
        // Add all files if requested
        if (args.addAll) {
          await git.addFiles(['.']);
        }
        
        logger.debug(`Committing with message: ${message}`);
        
        const result = await git.commit(message);
        
        return {
          success: true,
          message,
          hash: result.commit,
          summary: result.summary
        };
      } catch (error) {
        logger.error('Failed to commit changes:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
  });

  // Git push tool
  registry.registerTool({
    name: 'git_push',
    description: 'Push commits to remote repository',
    parameters: {
      type: 'object',
      properties: {
        remote: {
          type: 'string',
          description: 'Remote name (default: origin)',
          default: 'origin'
        },
        branch: {
          type: 'string',
          description: 'Branch name (default: current branch)'
        },
        force: {
          type: 'boolean',
          description: 'Force push',
          default: false
        }
      }
    },
    execute: async (args: any) => {
      try {
        const git = getGitManager();
        const remote = args.remote || 'origin';
        const branch = args.branch || await git.getCurrentBranch();
        const force = args.force || false;
        
        logger.debug(`Pushing to ${remote}/${branch}`);
        
        await git.push(remote, branch, force);
        
        return {
          success: true,
          remote,
          branch,
          force
        };
      } catch (error) {
        logger.error('Failed to push changes:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
  });

  // Git pull tool
  registry.registerTool({
    name: 'git_pull',
    description: 'Pull changes from remote repository',
    parameters: {
      type: 'object',
      properties: {
        remote: {
          type: 'string',
          description: 'Remote name (default: origin)',
          default: 'origin'
        },
        branch: {
          type: 'string',
          description: 'Branch name (default: current branch)'
        }
      }
    },
    execute: async (args: any) => {
      try {
        const git = getGitManager();
        const remote = args.remote || 'origin';
        const branch = args.branch || await git.getCurrentBranch();
        
        logger.debug(`Pulling from ${remote}/${branch}`);
        
        const result = await git.pull(remote, branch);
        
        return {
          success: true,
          remote,
          branch,
          summary: result.summary
        };
      } catch (error) {
        logger.error('Failed to pull changes:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
  });

  // Git branch tool
  registry.registerTool({
    name: 'git_branch',
    description: 'Manage git branches',
    parameters: {
      type: 'object',
      properties: {
        action: {
          type: 'string',
          enum: ['list', 'create', 'delete', 'switch'],
          description: 'Branch action to perform'
        },
        name: {
          type: 'string',
          description: 'Branch name (required for create, delete, switch)'
        },
        force: {
          type: 'boolean',
          description: 'Force action (for delete)',
          default: false
        }
      },
      required: ['action']
    },
    execute: async (args: any) => {
      try {
        const git = getGitManager();
        const action = args.action;
        const name = args.name;
        const force = args.force || false;
        
        logger.debug(`Git branch action: ${action} ${name || ''}`);
        
        switch (action) {
          case 'list':
            const branches = await git.getBranches();
            return {
              success: true,
              action,
              branches: branches.all,
              current: branches.current
            };
            
          case 'create':
            if (!name) {
              throw new Error('Branch name is required for create action');
            }
            await git.createBranch(name);
            return {
              success: true,
              action,
              name
            };
            
          case 'delete':
            if (!name) {
              throw new Error('Branch name is required for delete action');
            }
            await git.deleteBranch(name, force);
            return {
              success: true,
              action,
              name,
              force
            };
            
          case 'switch':
            if (!name) {
              throw new Error('Branch name is required for switch action');
            }
            await git.switchBranch(name);
            return {
              success: true,
              action,
              name
            };
            
          default:
            throw new Error(`Unknown branch action: ${action}`);
        }
      } catch (error) {
        logger.error('Failed to perform git branch operation:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
  });

  // Git log tool
  registry.registerTool({
    name: 'git_log',
    description: 'Get git commit history',
    parameters: {
      type: 'object',
      properties: {
        limit: {
          type: 'number',
          description: 'Number of commits to retrieve',
          default: 10
        },
        oneline: {
          type: 'boolean',
          description: 'Show one line per commit',
          default: false
        }
      }
    },
    execute: async (args: any) => {
      try {
        const git = getGitManager();
        const limit = args.limit || 10;
        
        logger.debug(`Getting git log (limit: ${limit})`);
        
        const commits = await git.getCommitHistory(limit);
        
        return {
          success: true,
          commits: commits.map(commit => ({
            hash: commit.hash,
            message: commit.message,
            author: commit.author_name,
            email: commit.author_email,
            date: commit.date
          }))
        };
      } catch (error) {
        logger.error('Failed to get git log:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
  });

  // Git diff tool
  registry.registerTool({
    name: 'git_diff',
    description: 'Show git differences',
    parameters: {
      type: 'object',
      properties: {
        staged: {
          type: 'boolean',
          description: 'Show staged changes',
          default: false
        },
        file: {
          type: 'string',
          description: 'Specific file to diff'
        }
      }
    },
    execute: async (args: any) => {
      try {
        const git = getGitManager();
        const staged = args.staged || false;
        const file = args.file;
        
        logger.debug(`Getting git diff (staged: ${staged}, file: ${file || 'all'})`);
        
        const diff = await git.getDiff(staged, file);
        
        return {
          success: true,
          diff,
          staged,
          file
        };
      } catch (error) {
        logger.error('Failed to get git diff:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
  });

  logger.info('Git operation tools registered successfully');
}
