import { ToolRegistry } from './ToolRegistry.js';
import { logger } from '../utils/logger.js';

// Import tool categories
import { registerFileTools } from './file/registerFileTools.js';
import { registerShellTools } from './shell/registerShellTools.js';
import { registerGitTools } from './git/registerGitTools.js';
import { registerSemanticTools } from './semantic/registerSemanticTools.js';
import { registerDiffTools } from './diff/registerDiffTools.js';
import { registerDebugTools } from './debug/registerDebugTools.js';
import { registerUtilityTools } from './utility/registerUtilityTools.js';

/**
 * Register all default tools
 * @returns Initialized ToolRegistry
 */
export function registerDefaultTools(): ToolRegistry {
  logger.info('Registering default tools');
  
  const toolRegistry = new ToolRegistry();
  
  try {
    // Register file tools
    registerFileTools(toolRegistry);
    
    // Register shell tools
    registerShellTools(toolRegistry);
    
    // Register git tools
    registerGitTools(toolRegistry);
    
    // Register semantic tools
    registerSemanticTools(toolRegistry);
    
    // Register diff tools
    registerDiffTools(toolRegistry);
    
    // Register debug tools
    registerDebugTools(toolRegistry);
    
    // Register utility tools
    registerUtilityTools(toolRegistry);
    
    logger.info(`Registered ${toolRegistry.getToolCount()} tools`);
  } catch (error) {
    logger.error('Error registering tools:', error);
  }
  
  return toolRegistry;
} 