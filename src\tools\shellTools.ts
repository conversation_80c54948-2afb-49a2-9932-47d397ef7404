import { z } from 'zod';
import { exec } from 'child_process';
import { promisify } from 'util';
import { ToolRegistry } from './ToolRegistry.js';
import { logger } from '../utils/logger.js';

const execAsync = promisify(exec);

/**
 * Register all shell-related tools
 */
export function registerShellTools(registry: ToolRegistry): void {
  logger.info('Registering shell tools');
  
  // Execute Shell Command Tool
  registry.registerTool({
    name: 'shell_exec',
    description: 'Execute a shell command',
    parameters: z.object({
      command: z.string().describe('The shell command to execute')
    }),
    execute: async (params: { command: string }) => {
      try {
        const { stdout, stderr } = await execAsync(params.command);
        
        return {
          success: !stderr || stderr.length === 0,
          stdout,
          stderr,
          command: params.command
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        return {
          success: false,
          stdout: '',
          stderr: errorMessage,
          command: params.command
        };
      }
    }
  });
  
  // Get Environment Variables Tool
  registry.registerTool({
    name: 'shell_env',
    description: 'Get environment variables',
    parameters: z.object({
      variables: z.array(z.string()).optional().describe('List of environment variables to get. If not provided, returns all environment variables.')
    }),
    execute: async (params: { variables?: string[] }) => {
      try {
        const env: Record<string, string> = {};
        
        if (params.variables && params.variables.length > 0) {
          // Get specific environment variables
          for (const variable of params.variables) {
            if (process.env[variable]) {
              env[variable] = process.env[variable] as string;
            }
          }
        } else {
          // Get all environment variables
          Object.assign(env, process.env);
        }
        
        return {
          success: true,
          env
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        return {
          success: false,
          error: errorMessage
        };
      }
    }
  });
  
  // Get Current Working Directory Tool
  registry.registerTool({
    name: 'shell_pwd',
    description: 'Get the current working directory',
    parameters: z.object({}),
    execute: async () => {
      try {
        const cwd = process.cwd();
        
        return {
          success: true,
          cwd
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        return {
          success: false,
          error: errorMessage
        };
      }
    }
  });
  
  // Change Directory Tool
  registry.registerTool({
    name: 'shell_cd',
    description: 'Change the current working directory',
    parameters: z.object({
      path: z.string().describe('Path to change to')
    }),
    execute: async (params: { path: string }) => {
      try {
        const oldCwd = process.cwd();
        process.chdir(params.path);
        const newCwd = process.cwd();
        
        return {
          success: true,
          oldCwd,
          newCwd
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        return {
          success: false,
          error: errorMessage,
          cwd: process.cwd()
        };
      }
    }
  });
  
  // Interactive Command Tool (with pseudo TTY)
  registry.registerTool({
    name: 'shell_interactive',
    description: 'Execute an interactive shell command with pseudo TTY',
    parameters: z.object({
      command: z.string().describe('The shell command to execute'),
      input: z.string().optional().describe('Optional input to send to the command')
    }),
    execute: async (params: { command: string; input?: string }) => {
      try {
        // For interactive commands requiring a TTY, we'll use node-pty in a real implementation
        // This is a simplified version that uses exec
        logger.warn('Using simplified interactive command execution');
        
        const { stdout, stderr } = await execAsync(params.command);
        
        return {
          success: !stderr || stderr.length === 0,
          output: stdout,
          error: stderr,
          command: params.command
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        return {
          success: false,
          output: '',
          error: errorMessage,
          command: params.command
        };
      }
    }
  });
  
  logger.info('Shell tools registered successfully');
} 