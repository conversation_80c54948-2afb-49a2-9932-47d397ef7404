import { ToolRegistry } from '../ToolRegistry.js';
import { logger } from '../../utils/logger.js';
import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import os from 'os';

const execAsync = promisify(exec);

/**
 * Register all shell operation tools
 */
export function registerShellTools(registry: ToolRegistry): void {
  logger.info('Registering shell operation tools');

  // Execute command tool
  registry.registerTool({
    name: 'execute_command',
    description: 'Execute a shell command',
    parameters: {
      type: 'object',
      properties: {
        command: {
          type: 'string',
          description: 'Command to execute'
        },
        args: {
          type: 'array',
          items: { type: 'string' },
          description: 'Command arguments',
          default: []
        },
        cwd: {
          type: 'string',
          description: 'Working directory for the command',
          default: process.cwd()
        },
        timeout: {
          type: 'number',
          description: 'Timeout in milliseconds',
          default: 30000
        },
        shell: {
          type: 'boolean',
          description: 'Run command in shell',
          default: true
        }
      },
      required: ['command']
    },
    execute: async (args: any) => {
      try {
        const command = args.command;
        const commandArgs = args.args || [];
        const cwd = args.cwd || process.cwd();
        const timeout = args.timeout || 30000;
        const useShell = args.shell !== false;
        
        logger.debug(`Executing command: ${command} ${commandArgs.join(' ')}`);
        
        return new Promise((resolve) => {
          const child = spawn(command, commandArgs, {
            cwd,
            shell: useShell,
            stdio: ['pipe', 'pipe', 'pipe']
          });
          
          let stdout = '';
          let stderr = '';
          let timedOut = false;
          
          // Set timeout
          const timer = setTimeout(() => {
            timedOut = true;
            child.kill('SIGTERM');
          }, timeout);
          
          // Collect output
          child.stdout?.on('data', (data) => {
            stdout += data.toString();
          });
          
          child.stderr?.on('data', (data) => {
            stderr += data.toString();
          });
          
          // Handle completion
          child.on('close', (code) => {
            clearTimeout(timer);
            
            if (timedOut) {
              resolve({
                success: false,
                error: 'Command timed out',
                timeout: true,
                stdout,
                stderr
              });
            } else {
              resolve({
                success: code === 0,
                exitCode: code,
                stdout,
                stderr,
                command: `${command} ${commandArgs.join(' ')}`
              });
            }
          });
          
          // Handle errors
          child.on('error', (error) => {
            clearTimeout(timer);
            resolve({
              success: false,
              error: error.message,
              stdout,
              stderr
            });
          });
        });
      } catch (error) {
        logger.error(`Failed to execute command ${args.command}:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
  });

  // Execute shell script tool
  registry.registerTool({
    name: 'execute_script',
    description: 'Execute a shell script',
    parameters: {
      type: 'object',
      properties: {
        script: {
          type: 'string',
          description: 'Shell script content to execute'
        },
        cwd: {
          type: 'string',
          description: 'Working directory for the script',
          default: process.cwd()
        },
        timeout: {
          type: 'number',
          description: 'Timeout in milliseconds',
          default: 30000
        },
        env: {
          type: 'object',
          description: 'Environment variables',
          default: {}
        }
      },
      required: ['script']
    },
    execute: async (args: any) => {
      try {
        const script = args.script;
        const cwd = args.cwd || process.cwd();
        const timeout = args.timeout || 30000;
        const env = { ...process.env, ...args.env };
        
        logger.debug(`Executing script in ${cwd}`);
        
        const { stdout, stderr } = await execAsync(script, {
          cwd,
          timeout,
          env,
          maxBuffer: 1024 * 1024 // 1MB buffer
        });
        
        return {
          success: true,
          stdout,
          stderr,
          script
        };
      } catch (error: any) {
        logger.error(`Failed to execute script:`, error);
        return {
          success: false,
          error: error.message,
          stdout: error.stdout || '',
          stderr: error.stderr || '',
          exitCode: error.code
        };
      }
    }
  });

  // Get environment variables tool
  registry.registerTool({
    name: 'get_environment',
    description: 'Get environment variables',
    parameters: {
      type: 'object',
      properties: {
        variable: {
          type: 'string',
          description: 'Specific environment variable to get (optional)'
        }
      }
    },
    execute: async (args: any) => {
      try {
        if (args.variable) {
          const value = process.env[args.variable];
          return {
            success: true,
            variable: args.variable,
            value: value || null
          };
        } else {
          return {
            success: true,
            environment: process.env
          };
        }
      } catch (error) {
        logger.error('Failed to get environment variables:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
  });

  // Set environment variable tool
  registry.registerTool({
    name: 'set_environment',
    description: 'Set an environment variable for the current session',
    parameters: {
      type: 'object',
      properties: {
        variable: {
          type: 'string',
          description: 'Environment variable name'
        },
        value: {
          type: 'string',
          description: 'Environment variable value'
        }
      },
      required: ['variable', 'value']
    },
    execute: async (args: any) => {
      try {
        process.env[args.variable] = args.value;
        
        logger.debug(`Set environment variable: ${args.variable}=${args.value}`);
        
        return {
          success: true,
          variable: args.variable,
          value: args.value
        };
      } catch (error) {
        logger.error(`Failed to set environment variable ${args.variable}:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
  });

  // Get system information tool
  registry.registerTool({
    name: 'get_system_info',
    description: 'Get system information',
    parameters: {
      type: 'object',
      properties: {}
    },
    execute: async () => {
      try {
        const systemInfo = {
          platform: os.platform(),
          arch: os.arch(),
          release: os.release(),
          hostname: os.hostname(),
          uptime: os.uptime(),
          loadavg: os.loadavg(),
          totalmem: os.totalmem(),
          freemem: os.freemem(),
          cpus: os.cpus().length,
          networkInterfaces: Object.keys(os.networkInterfaces()),
          nodeVersion: process.version,
          pid: process.pid,
          cwd: process.cwd(),
          execPath: process.execPath
        };
        
        return {
          success: true,
          systemInfo
        };
      } catch (error) {
        logger.error('Failed to get system information:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
  });

  // Change directory tool
  registry.registerTool({
    name: 'change_directory',
    description: 'Change the current working directory',
    parameters: {
      type: 'object',
      properties: {
        path: {
          type: 'string',
          description: 'Path to change to'
        }
      },
      required: ['path']
    },
    execute: async (args: any) => {
      try {
        const oldCwd = process.cwd();
        process.chdir(args.path);
        const newCwd = process.cwd();
        
        logger.debug(`Changed directory: ${oldCwd} -> ${newCwd}`);
        
        return {
          success: true,
          oldPath: oldCwd,
          newPath: newCwd
        };
      } catch (error) {
        logger.error(`Failed to change directory to ${args.path}:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
  });

  // Kill process tool
  registry.registerTool({
    name: 'kill_process',
    description: 'Kill a process by PID',
    parameters: {
      type: 'object',
      properties: {
        pid: {
          type: 'number',
          description: 'Process ID to kill'
        },
        signal: {
          type: 'string',
          description: 'Signal to send (default: SIGTERM)',
          default: 'SIGTERM'
        }
      },
      required: ['pid']
    },
    execute: async (args: any) => {
      try {
        const pid = args.pid;
        const signal = args.signal || 'SIGTERM';
        
        process.kill(pid, signal);
        
        logger.debug(`Killed process ${pid} with signal ${signal}`);
        
        return {
          success: true,
          pid,
          signal
        };
      } catch (error) {
        logger.error(`Failed to kill process ${args.pid}:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    }
  });

  logger.info('Shell operation tools registered successfully');
}
