import readline from 'readline';
import chalk from 'chalk';
import { Config } from '../utils/config.js';
import { logger } from '../utils/logger.js';
import { 
  displayBanner, 
  displayCompactBanner, 
  displayHelp, 
  displayError, 
  displaySuccess, 
  displayWarning, 
  displayInfo,
  displayProviderStatus,
  displaySeparator,
  displayFooter
} from './banner.js';

/**
 * Terminal UI manager for interactive CLI interface
 */
export class TerminalUI {
  private rl: readline.Interface;
  private config: Config;
  private isFirstRun: boolean = true;
  private commandHistory: string[] = [];
  private currentProvider: string = '';
  private availableProviders: string[] = [];

  constructor(config: Config) {
    this.config = config;
    this.setupReadline();
  }

  /**
   * Setup readline interface with enhanced features
   */
  private setupReadline(): void {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: this.getPrompt(),
      completer: this.completer.bind(this),
      history: this.commandHistory,
      historySize: 100,
      removeHistoryDuplicates: true
    });

    // Handle Ctrl+C gracefully
    this.rl.on('SIGINT', () => {
      console.log('\n');
      this.displayGoodbye();
      process.exit(0);
    });

    // Handle line input
    this.rl.on('line', (input) => {
      this.handleInput(input.trim());
    });

    // Handle close event
    this.rl.on('close', () => {
      this.displayGoodbye();
      process.exit(0);
    });
  }

  /**
   * Start the interactive terminal session
   */
  public start(): void {
    // Display banner
    if (this.isFirstRun) {
      displayBanner(this.config);
      this.isFirstRun = false;
    } else {
      displayCompactBanner(this.config);
    }

    // Show provider status if available
    if (this.availableProviders.length > 0) {
      displayProviderStatus(this.availableProviders, this.currentProvider);
    }

    // Start prompting for input
    this.prompt();
  }

  /**
   * Display the prompt and wait for input
   */
  public prompt(): void {
    this.rl.setPrompt(this.getPrompt());
    this.rl.prompt();
  }

  /**
   * Generate the command prompt
   */
  private getPrompt(): string {
    const cwd = process.cwd().split('/').pop() || process.cwd().split('\\').pop() || 'unknown';
    const provider = this.currentProvider ? chalk.yellow(`[${this.currentProvider}]`) : '';
    const autonomous = chalk.red('[AUTO]');
    
    return `${chalk.blue('🤖')} ${chalk.cyan(cwd)} ${provider} ${autonomous} ${chalk.green('❯')} `;
  }

  /**
   * Handle user input
   */
  private async handleInput(input: string): Promise<void> {
    if (!input) {
      this.prompt();
      return;
    }

    // Add to command history
    this.commandHistory.push(input);

    // Handle built-in commands
    if (await this.handleBuiltInCommands(input)) {
      this.prompt();
      return;
    }

    // Emit the input for the agent to process
    this.emit('userInput', input);
  }

  /**
   * Handle built-in terminal commands
   */
  private async handleBuiltInCommands(input: string): Promise<boolean> {
    const [command, ...args] = input.split(' ');

    switch (command.toLowerCase()) {
      case 'help':
      case '?':
        displayHelp();
        return true;

      case 'clear':
      case 'cls':
        console.clear();
        displayCompactBanner(this.config);
        return true;

      case 'exit':
      case 'quit':
      case 'q':
        this.close();
        return true;

      case 'history':
        this.displayHistory();
        return true;

      case 'status':
        this.displayStatus();
        return true;

      case 'providers':
        this.displayProviders();
        return true;

      case 'config':
        this.displayConfig();
        return true;

      case 'version':
        this.displayVersion();
        return true;

      default:
        return false; // Not a built-in command
    }
  }

  /**
   * Auto-completion for commands
   */
  private completer(line: string): [string[], string] {
    const commands = [
      'help', 'clear', 'exit', 'quit', 'history', 'status', 'providers', 'config', 'version',
      'analyze', 'search', 'diff', 'debug', 'context', 'switch', 'model',
      'git status', 'git commit', 'git push', 'git pull', 'git branch'
    ];

    const hits = commands.filter((c) => c.startsWith(line));
    return [hits.length ? hits : commands, line];
  }

  /**
   * Display command history
   */
  private displayHistory(): void {
    console.log(chalk.blue.bold('\n📜 Command History:'));
    
    if (this.commandHistory.length === 0) {
      console.log(chalk.gray('No commands in history.'));
    } else {
      this.commandHistory.slice(-10).forEach((cmd, index) => {
        const number = this.commandHistory.length - 10 + index + 1;
        console.log(`${chalk.gray(`${number}.`)} ${chalk.white(cmd)}`);
      });
    }
    
    console.log('');
  }

  /**
   * Display system status
   */
  private displayStatus(): void {
    console.log(chalk.blue.bold('\n🔍 System Status:'));
    console.log(`${chalk.gray('├─')} ${chalk.white('Working Directory:')} ${chalk.yellow(process.cwd())}`);
    console.log(`${chalk.gray('├─')} ${chalk.white('Node.js Version:')} ${chalk.yellow(process.version)}`);
    console.log(`${chalk.gray('├─')} ${chalk.white('Platform:')} ${chalk.yellow(process.platform)}`);
    console.log(`${chalk.gray('├─')} ${chalk.white('Architecture:')} ${chalk.yellow(process.arch)}`);
    console.log(`${chalk.gray('├─')} ${chalk.white('Memory Usage:')} ${chalk.yellow(`${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`)}`);
    console.log(`${chalk.gray('└─')} ${chalk.white('Uptime:')} ${chalk.yellow(`${Math.round(process.uptime())}s`)}`);
    console.log('');
  }

  /**
   * Display available providers
   */
  private displayProviders(): void {
    if (this.availableProviders.length === 0) {
      displayWarning('No AI providers are currently available.');
      return;
    }

    displayProviderStatus(this.availableProviders, this.currentProvider);
  }

  /**
   * Display current configuration
   */
  private displayConfig(): void {
    console.log(chalk.blue.bold('\n⚙️  Current Configuration:'));
    console.log(`${chalk.gray('├─')} ${chalk.white('Default Provider:')} ${chalk.yellow(this.config.ai.defaultProvider || 'Auto-detect')}`);
    console.log(`${chalk.gray('├─')} ${chalk.white('Model:')} ${chalk.yellow(this.config.ai.model || 'Provider default')}`);
    console.log(`${chalk.gray('├─')} ${chalk.white('Temperature:')} ${chalk.yellow(this.config.ai.temperature?.toString() || '0.7')}`);
    console.log(`${chalk.gray('├─')} ${chalk.white('Max Tokens:')} ${chalk.yellow(this.config.ai.maxTokens?.toString() || '4000')}`);
    console.log(`${chalk.gray('├─')} ${chalk.white('System Prompt:')} ${chalk.yellow(this.config.ai.systemPrompt ? 'Custom' : 'Default')}`);
    console.log(`${chalk.gray('└─')} ${chalk.white('Autonomous Mode:')} ${chalk.green('Enabled')}`);
    console.log('');
  }

  /**
   * Display version information
   */
  private displayVersion(): void {
    const packageJson = require('../../package.json');
    console.log(chalk.blue.bold('\n📦 Version Information:'));
    console.log(`${chalk.gray('├─')} ${chalk.white('Application:')} ${chalk.yellow(packageJson.name || 'AI Agent CLI')}`);
    console.log(`${chalk.gray('├─')} ${chalk.white('Version:')} ${chalk.yellow(packageJson.version || '1.0.0')}`);
    console.log(`${chalk.gray('├─')} ${chalk.white('Node.js:')} ${chalk.yellow(process.version)}`);
    console.log(`${chalk.gray('└─')} ${chalk.white('Platform:')} ${chalk.yellow(`${process.platform}-${process.arch}`)}`);
    console.log('');
  }

  /**
   * Display goodbye message
   */
  private displayGoodbye(): void {
    displayFooter();
  }

  /**
   * Update provider information
   */
  public updateProviders(providers: string[], current: string): void {
    this.availableProviders = providers;
    this.currentProvider = current;
  }

  /**
   * Display a message to the user
   */
  public displayMessage(message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info'): void {
    switch (type) {
      case 'success':
        displaySuccess(message);
        break;
      case 'warning':
        displayWarning(message);
        break;
      case 'error':
        displayError(message);
        break;
      default:
        displayInfo(message);
        break;
    }
  }

  /**
   * Display a separator
   */
  public displaySeparator(): void {
    displaySeparator();
  }

  /**
   * Write text without a newline
   */
  public write(text: string): void {
    process.stdout.write(text);
  }

  /**
   * Write a line of text
   */
  public writeLine(text: string): void {
    console.log(text);
  }

  /**
   * Close the terminal interface
   */
  public close(): void {
    this.rl.close();
  }

  /**
   * Simple event emitter functionality
   */
  private listeners: { [event: string]: Function[] } = {};

  public on(event: string, listener: Function): void {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(listener);
  }

  public emit(event: string, ...args: any[]): void {
    if (this.listeners[event]) {
      this.listeners[event].forEach(listener => listener(...args));
    }
  }

  /**
   * Remove event listener
   */
  public off(event: string, listener: Function): void {
    if (this.listeners[event]) {
      const index = this.listeners[event].indexOf(listener);
      if (index > -1) {
        this.listeners[event].splice(index, 1);
      }
    }
  }
}
