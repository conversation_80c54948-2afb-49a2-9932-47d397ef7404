import chalk from 'chalk';
import { Config } from '../utils/config.js';
import { logger } from '../utils/logger.js';

/**
 * Display the application banner with system information
 */
export function displayBanner(config: Config): void {
  const banner = `
${chalk.cyan.bold('╔══════════════════════════════════════════════════════════════════════════════╗')}
${chalk.cyan.bold('║')}                                                                              ${chalk.cyan.bold('║')}
${chalk.cyan.bold('║')}    ${chalk.yellow.bold('🤖 AUTONOMOUS AI AGENT CLI TOOL')}                                      ${chalk.cyan.bold('║')}
${chalk.cyan.bold('║')}                                                                              ${chalk.cyan.bold('║')}
${chalk.cyan.bold('║')}    ${chalk.green('Fully autonomous AI-powered development assistant')}                    ${chalk.cyan.bold('║')}
${chalk.cyan.bold('║')}    ${chalk.green('Direct file system access • No approval required')}                   ${chalk.cyan.bold('║')}
${chalk.cyan.bold('║')}    ${chalk.green('Multi-provider AI support • Advanced code understanding')}            ${chalk.cyan.bold('║')}
${chalk.cyan.bold('║')}                                                                              ${chalk.cyan.bold('║')}
${chalk.cyan.bold('╚══════════════════════════════════════════════════════════════════════════════╝')}

${chalk.blue.bold('🔧 System Information:')}
${chalk.gray('├─')} ${chalk.white('Working Directory:')} ${chalk.yellow(process.cwd())}
${chalk.gray('├─')} ${chalk.white('Operating System:')} ${chalk.yellow(process.platform)} ${chalk.gray(process.arch)}
${chalk.gray('├─')} ${chalk.white('Node.js Version:')} ${chalk.yellow(process.version)}
${chalk.gray('└─')} ${chalk.white('Shell Environment:')} ${chalk.yellow(process.env.SHELL || process.env.COMSPEC || 'Unknown')}

${chalk.blue.bold('🚀 AI Configuration:')}
${chalk.gray('├─')} ${chalk.white('Default Provider:')} ${chalk.yellow(config.ai.defaultProvider || 'Auto-detect')}
${chalk.gray('├─')} ${chalk.white('Model:')} ${chalk.yellow(config.ai.model || 'Provider default')}
${chalk.gray('├─')} ${chalk.white('Temperature:')} ${chalk.yellow(config.ai.temperature?.toString() || '0.7')}
${chalk.gray('└─')} ${chalk.white('Max Tokens:')} ${chalk.yellow(config.ai.maxTokens?.toString() || '4000')}

${chalk.blue.bold('⚡ Capabilities:')}
${chalk.gray('├─')} ${chalk.green('✓')} File Operations (read, write, copy, move, delete)
${chalk.gray('├─')} ${chalk.green('✓')} Shell Command Execution
${chalk.gray('├─')} ${chalk.green('✓')} Git Repository Management
${chalk.gray('├─')} ${chalk.green('✓')} Semantic Code Search & Analysis
${chalk.gray('├─')} ${chalk.green('✓')} Real-time Diff Visualization
${chalk.gray('├─')} ${chalk.green('✓')} Autonomous Debugging & Error Resolution
${chalk.gray('├─')} ${chalk.green('✓')} Context-aware Conversation Memory
${chalk.gray('└─')} ${chalk.green('✓')} Multi-provider AI Support (OpenAI, Deepseek, Ollama)

${chalk.blue.bold('🛡️  Safety Features:')}
${chalk.gray('├─')} ${chalk.green('✓')} Complete audit trail of all actions
${chalk.gray('├─')} ${chalk.green('✓')} Comprehensive diff tracking
${chalk.gray('├─')} ${chalk.green('✓')} Git integration with repository protection
${chalk.gray('├─')} ${chalk.green('✓')} Runtime monitoring and error detection
${chalk.gray('└─')} ${chalk.green('✓')} Semantic safety analysis

${chalk.yellow.bold('⚠️  AUTONOMOUS MODE ACTIVE')}
${chalk.red('This agent will execute commands and modify files automatically.')}
${chalk.red('All actions are logged and can be reviewed in the audit trail.')}

${chalk.green.bold('Ready to assist! Type your request or "help" for available commands.')}
`;

  console.log(banner);
  logger.info('Application banner displayed');
}

/**
 * Display a compact banner for subsequent runs
 */
export function displayCompactBanner(config: Config): void {
  const compactBanner = `
${chalk.cyan.bold('🤖 AI Agent CLI')} ${chalk.gray('|')} ${chalk.yellow(config.ai.defaultProvider || 'Auto')} ${chalk.gray('|')} ${chalk.green('Autonomous Mode')}
${chalk.gray('─'.repeat(60))}`;

  console.log(compactBanner);
}

/**
 * Display provider status information
 */
export function displayProviderStatus(providers: string[], currentProvider: string): void {
  console.log(chalk.blue.bold('\n📡 Provider Status:'));
  
  providers.forEach(provider => {
    const isCurrent = provider === currentProvider;
    const status = isCurrent ? chalk.green('● ACTIVE') : chalk.gray('○ Available');
    const name = isCurrent ? chalk.yellow.bold(provider) : chalk.white(provider);
    
    console.log(`${chalk.gray('├─')} ${name} ${status}`);
  });
  
  console.log(chalk.gray('└─') + chalk.gray(' Use "switch <provider>" to change active provider\n'));
}

/**
 * Display help information
 */
export function displayHelp(): void {
  const help = `
${chalk.blue.bold('📚 Available Commands:')}

${chalk.yellow.bold('General Commands:')}
${chalk.gray('├─')} ${chalk.green('help')} - Show this help message
${chalk.gray('├─')} ${chalk.green('status')} - Show system and provider status
${chalk.gray('├─')} ${chalk.green('clear')} - Clear the terminal screen
${chalk.gray('├─')} ${chalk.green('exit')} - Exit the application
${chalk.gray('└─')} ${chalk.green('version')} - Show version information

${chalk.yellow.bold('AI Provider Commands:')}
${chalk.gray('├─')} ${chalk.green('providers')} - List available AI providers
${chalk.gray('├─')} ${chalk.green('switch <provider>')} - Switch to a different AI provider
${chalk.gray('├─')} ${chalk.green('model <name>')} - Change the AI model
${chalk.gray('└─')} ${chalk.green('config')} - Show current configuration

${chalk.yellow.bold('Development Commands:')}
${chalk.gray('├─')} ${chalk.green('analyze <path>')} - Analyze code structure
${chalk.gray('├─')} ${chalk.green('search <query>')} - Semantic code search
${chalk.gray('├─')} ${chalk.green('diff')} - Show recent file changes
${chalk.gray('├─')} ${chalk.green('git status')} - Show git repository status
${chalk.gray('├─')} ${chalk.green('debug <file>')} - Debug and fix errors
${chalk.gray('└─')} ${chalk.green('context')} - Show current context information

${chalk.yellow.bold('Natural Language:')}
${chalk.gray('└─')} Simply describe what you want to do in natural language!
    ${chalk.gray('Examples:')}
    ${chalk.gray('•')} "Create a new React component for user authentication"
    ${chalk.gray('•')} "Fix the TypeScript errors in the utils folder"
    ${chalk.gray('•')} "Refactor the database connection code"
    ${chalk.gray('•')} "Add unit tests for the API endpoints"

${chalk.blue.bold('🔍 Features:')}
${chalk.gray('├─')} ${chalk.green('Autonomous Operation:')} No approval needed for actions
${chalk.gray('├─')} ${chalk.green('Context Awareness:')} Understands your project structure
${chalk.gray('├─')} ${chalk.green('Multi-language Support:')} Works with any programming language
${chalk.gray('├─')} ${chalk.green('Git Integration:')} Automatic version control management
${chalk.gray('├─')} ${chalk.green('Real-time Feedback:')} Live updates as actions are performed
${chalk.gray('└─')} ${chalk.green('Comprehensive Logging:')} Full audit trail of all operations

${chalk.yellow('💡 Tip:')} The agent learns from your project context and conversation history.
${chalk.yellow('💡 Tip:')} Use specific, detailed requests for better results.
${chalk.yellow('💡 Tip:')} Check the logs for detailed information about performed actions.
`;

  console.log(help);
}

/**
 * Display error message with formatting
 */
export function displayError(message: string, details?: string): void {
  console.log(`\n${chalk.red.bold('❌ Error:')} ${chalk.red(message)}`);
  
  if (details) {
    console.log(`${chalk.gray('Details:')} ${chalk.gray(details)}`);
  }
  
  console.log('');
}

/**
 * Display success message with formatting
 */
export function displaySuccess(message: string, details?: string): void {
  console.log(`\n${chalk.green.bold('✅ Success:')} ${chalk.green(message)}`);
  
  if (details) {
    console.log(`${chalk.gray('Details:')} ${chalk.gray(details)}`);
  }
  
  console.log('');
}

/**
 * Display warning message with formatting
 */
export function displayWarning(message: string, details?: string): void {
  console.log(`\n${chalk.yellow.bold('⚠️  Warning:')} ${chalk.yellow(message)}`);
  
  if (details) {
    console.log(`${chalk.gray('Details:')} ${chalk.gray(details)}`);
  }
  
  console.log('');
}

/**
 * Display info message with formatting
 */
export function displayInfo(message: string, details?: string): void {
  console.log(`\n${chalk.blue.bold('ℹ️  Info:')} ${chalk.blue(message)}`);
  
  if (details) {
    console.log(`${chalk.gray('Details:')} ${chalk.gray(details)}`);
  }
  
  console.log('');
}

/**
 * Display loading spinner with message
 */
export function displayLoading(message: string): void {
  process.stdout.write(`${chalk.blue('⏳')} ${message}...`);
}

/**
 * Clear the loading message
 */
export function clearLoading(): void {
  process.stdout.write('\r\x1b[K'); // Clear the current line
}

/**
 * Display a separator line
 */
export function displaySeparator(): void {
  console.log(chalk.gray('─'.repeat(80)));
}

/**
 * Display the application footer
 */
export function displayFooter(): void {
  console.log(`\n${chalk.gray('─'.repeat(80))}`);
  console.log(chalk.gray('Thank you for using the Autonomous AI Agent CLI Tool!'));
  console.log(chalk.gray('All actions have been logged for your review.'));
  console.log(`${chalk.gray('─'.repeat(80))}\n`);
}
