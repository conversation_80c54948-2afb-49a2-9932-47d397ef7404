import fs from 'fs/promises';
import path from 'path';
import os from 'os';
import { fileURLToPath } from 'url';
import { logger } from './logger.js';
import { isomorphicPath } from './isomorphicPath.js';

/**
 * Configuration interface
 */
export interface Config {
  ai: {
    defaultProvider?: string;
    model?: string;
    temperature?: number;
    maxTokens?: number;
    providers: {
      openai?: {
        apiKey?: string;
        baseUrl?: string;
        defaultModel?: string;
      };
      deepseek?: {
        apiKey?: string;
        baseUrl?: string;
        defaultModel?: string;
      };
      ollama?: {
        baseUrl?: string;
        defaultModel?: string;
      };
    };
  };
  tools: {
    allowedCommands?: string[];
    disallowedCommands?: string[];
    environmentVariables?: Record<string, string>;
  };
  context: {
    maxHistoryItems?: number;
    persistContext?: boolean;
    contextStoragePath?: string;
  };
  ui: {
    theme?: string;
    showDiffs?: boolean;
    verboseLogging?: boolean;
  };
  git: {
    enabled?: boolean;
    autoPush?: boolean;
  };
  semantic: {
    enabled?: boolean;
    embeddingModel?: string;
    storagePath?: string;
  };
  debug: {
    enabled?: boolean;
    logLevel?: string;
  };
}

/**
 * Default configuration
 */
const defaultConfig: Config = {
  ai: {
    defaultProvider: 'openai',
    model: 'gpt-4o',
    temperature: 0.2,
    maxTokens: 4000,
    providers: {
      openai: {
        baseUrl: 'https://api.openai.com/v1',
        defaultModel: 'gpt-4o'
      },
      deepseek: {
        baseUrl: 'https://api.deepseek.com/v1',
        defaultModel: 'deepseek-coder'
      },
      ollama: {
        baseUrl: 'http://localhost:11434',
        defaultModel: 'llama3'
      }
    }
  },
  tools: {
    allowedCommands: ['*'],
    disallowedCommands: ['rm -rf /', 'sudo', 'shutdown', 'reboot'],
    environmentVariables: {}
  },
  context: {
    maxHistoryItems: 20,
    persistContext: true,
    contextStoragePath: '.agent/context'
  },
  ui: {
    theme: 'default',
    showDiffs: true,
    verboseLogging: false
  },
  git: {
    enabled: true,
    autoPush: false
  },
  semantic: {
    enabled: true,
    embeddingModel: 'text-embedding-3-small',
    storagePath: '.agent/semantic'
  },
  debug: {
    enabled: true,
    logLevel: 'info'
  }
};

/**
 * Get the configuration file path
 */
async function getConfigPath(customPath?: string): Promise<string> {
  if (customPath) {
    return path.resolve(customPath);
  }
  
  // Check for config in current directory
  try {
    const localConfigPath = path.join(process.cwd(), '.agentrc.json');
    await fs.access(localConfigPath);
    return localConfigPath;
  } catch (error) {
    // Config not found in current directory
  }
  
  // Check for config in home directory
  const homeConfigPath = path.join(os.homedir(), '.agentrc.json');
  return homeConfigPath;
}

/**
 * Load configuration from file
 */
async function loadConfigFile(configPath: string): Promise<Partial<Config>> {
  try {
    const configData = await fs.readFile(configPath, 'utf-8');
    return JSON.parse(configData);
  } catch (error) {
    // If file doesn't exist, return empty config
    if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
      return {};
    }
    
    // For other errors, log and return empty config
    logger.error(`Error loading config from ${configPath}:`, error);
    return {};
  }
}

/**
 * Save configuration to file
 */
async function saveConfigFile(configPath: string, config: Config): Promise<void> {
  try {
    // Create directory if it doesn't exist
    const configDir = path.dirname(configPath);
    await fs.mkdir(configDir, { recursive: true });
    
    // Write config file
    await fs.writeFile(configPath, JSON.stringify(config, null, 2), 'utf-8');
    logger.info(`Configuration saved to ${configPath}`);
  } catch (error) {
    logger.error(`Error saving config to ${configPath}:`, error);
    throw error;
  }
}

/**
 * Merge configurations
 */
function mergeConfigs(baseConfig: Config, overrides: Partial<Config>): Config {
  return {
    ...baseConfig,
    ...overrides,
    ai: {
      ...baseConfig.ai,
      ...overrides.ai,
      providers: {
        ...baseConfig.ai.providers,
        ...overrides.ai?.providers,
        openai: {
          ...baseConfig.ai.providers.openai,
          ...overrides.ai?.providers?.openai
        },
        deepseek: {
          ...baseConfig.ai.providers.deepseek,
          ...overrides.ai?.providers?.deepseek
        },
        ollama: {
          ...baseConfig.ai.providers.ollama,
          ...overrides.ai?.providers?.ollama
        }
      }
    },
    tools: {
      ...baseConfig.tools,
      ...overrides.tools,
      environmentVariables: {
        ...baseConfig.tools.environmentVariables,
        ...overrides.tools?.environmentVariables
      }
    },
    context: {
      ...baseConfig.context,
      ...overrides.context
    },
    ui: {
      ...baseConfig.ui,
      ...overrides.ui
    },
    git: {
      ...baseConfig.git,
      ...overrides.git
    },
    semantic: {
      ...baseConfig.semantic,
      ...overrides.semantic
    },
    debug: {
      ...baseConfig.debug,
      ...overrides.debug
    }
  };
}

/**
 * Load environment variables into config
 */
function loadEnvVars(config: Config): Config {
  const updatedConfig = { ...config };
  
  // OpenAI
  if (process.env.OPENAI_API_KEY) {
    if (!updatedConfig.ai.providers.openai) {
      updatedConfig.ai.providers.openai = {};
    }
    updatedConfig.ai.providers.openai.apiKey = process.env.OPENAI_API_KEY;
  }
  
  // Deepseek
  if (process.env.DEEPSEEK_API_KEY) {
    if (!updatedConfig.ai.providers.deepseek) {
      updatedConfig.ai.providers.deepseek = {};
    }
    updatedConfig.ai.providers.deepseek.apiKey = process.env.DEEPSEEK_API_KEY;
  }
  
  // Override provider
  if (process.env.AI_PROVIDER) {
    updatedConfig.ai.defaultProvider = process.env.AI_PROVIDER;
  }
  
  // Override model
  if (process.env.AI_MODEL) {
    updatedConfig.ai.model = process.env.AI_MODEL;
  }
  
  return updatedConfig;
}

/**
 * Setup configuration
 */
export async function setupConfig(customConfigPath?: string, forceInteractive = false): Promise<Config> {
  try {
    // Get config path
    const configPath = await getConfigPath(customConfigPath);
    
    // Load config from file
    const fileConfig = await loadConfigFile(configPath);
    
    // Merge with default config
    let config = mergeConfigs(defaultConfig, fileConfig);
    
    // Load environment variables
    config = loadEnvVars(config);
    
    // TODO: Interactive configuration if forceInteractive is true
    if (forceInteractive) {
      // This would be implemented with a CLI prompt library
      logger.info('Interactive configuration not implemented yet');
    }
    
    return config;
  } catch (error) {
    logger.error('Error setting up configuration:', error);
    return defaultConfig;
  }
} 