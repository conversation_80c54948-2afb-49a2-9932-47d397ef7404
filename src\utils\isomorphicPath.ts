import path from 'path';
import os from 'os';

/**
 * Normalize a path for the current platform
 * @param filePath Path to normalize
 * @returns Normalized path
 */
export function isomorphicPath(filePath: string): string {
  // Get platform-specific path separator
  const sep = path.sep;
  
  // Normalize path separators
  let normalizedPath = filePath.replace(/[/\\]/g, sep);
  
  // Handle Windows drive letters
  if (os.platform() === 'win32' && !normalizedPath.match(/^[A-Z]:/i)) {
    // If path doesn't have a drive letter but starts with a separator,
    // assume it's relative to the current drive
    if (normalizedPath.startsWith(sep)) {
      const currentDrive = process.cwd().substring(0, 2);
      normalizedPath = `${currentDrive}${normalizedPath}`;
    }
  }
  
  return path.normalize(normalizedPath);
}

/**
 * Join path segments in a platform-independent way
 * @param segments Path segments to join
 * @returns Joined path
 */
export function joinPath(...segments: string[]): string {
  return isomorphicPath(path.join(...segments));
}

/**
 * Resolve a path in a platform-independent way
 * @param segments Path segments to resolve
 * @returns Resolved path
 */
export function resolvePath(...segments: string[]): string {
  return isomorphicPath(path.resolve(...segments));
}

/**
 * Get the directory name from a path
 * @param filePath Path to get directory from
 * @returns Directory path
 */
export function getDirName(filePath: string): string {
  return isomorphicPath(path.dirname(filePath));
}

/**
 * Get the base name from a path
 * @param filePath Path to get base name from
 * @param ext Optional extension to remove
 * @returns Base name
 */
export function getBaseName(filePath: string, ext?: string): string {
  return path.basename(isomorphicPath(filePath), ext);
}

/**
 * Get the extension from a path
 * @param filePath Path to get extension from
 * @returns File extension
 */
export function getExtension(filePath: string): string {
  return path.extname(isomorphicPath(filePath));
}

/**
 * Joins path segments using the correct separator for the current platform
 */
export function isomorphicJoin(...segments: string[]): string {
  // Use the path.join function to join the segments
  const joinedPath = path.join(...segments);
  
  // Convert to the correct format for the current platform
  return isomorphicPath(joinedPath);
}

/**
 * Resolves a path to an absolute path using the correct separator for the current platform
 */
export function isomorphicResolve(...segments: string[]): string {
  // Use the path.resolve function to resolve the segments
  const resolvedPath = path.resolve(...segments);
  
  // Convert to the correct format for the current platform
  return isomorphicPath(resolvedPath);
}

/**
 * Gets the directory name of a path using the correct separator for the current platform
 */
export function isomorphicDirname(inputPath: string): string {
  // Use the path.dirname function to get the directory name
  const dirname = path.dirname(inputPath);
  
  // Convert to the correct format for the current platform
  return isomorphicPath(dirname);
}

/**
 * Gets the base name of a path using the correct separator for the current platform
 */
export function isomorphicBasename(inputPath: string, ext?: string): string {
  // Use the path.basename function to get the base name
  return path.basename(inputPath, ext);
}

/**
 * Gets the extension of a path
 */
export function isomorphicExtname(inputPath: string): string {
  // Use the path.extname function to get the extension
  return path.extname(inputPath);
}

/**
 * Checks if a path is absolute
 */
export function isomorphicIsAbsolute(inputPath: string): boolean {
  // Use the path.isAbsolute function to check if the path is absolute
  return path.isAbsolute(inputPath);
}

/**
 * Gets the relative path from one path to another
 */
export function isomorphicRelative(from: string, to: string): string {
  // Use the path.relative function to get the relative path
  const relativePath = path.relative(from, to);
  
  // Convert to the correct format for the current platform
  return isomorphicPath(relativePath);
}

/**
 * Normalizes a path
 */
export function isomorphicNormalize(inputPath: string): string {
  // Use the path.normalize function to normalize the path
  const normalizedPath = path.normalize(inputPath);
  
  // Convert to the correct format for the current platform
  return isomorphicPath(normalizedPath);
}

/**
 * Parses a path into its components in a platform-independent way
 */
export function isomorphicParse(inputPath: string): path.ParsedPath {
  // Normalize the path first
  const normalizedPath = isomorphicPath(inputPath);
  
  // Parse the path
  return path.parse(normalizedPath);
}

/**
 * Formats path components into a path string in a platform-independent way
 */
export function isomorphicFormat(pathObject: path.FormatInputPathObject): string {
  // Format the path
  const formatted = path.format(pathObject);
  
  // Normalize the result
  return isomorphicPath(formatted);
} 