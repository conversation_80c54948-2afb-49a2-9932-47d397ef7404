import chalk from 'chalk';
import path from 'path';
import fs from 'fs/promises';
import { createWriteStream } from 'fs';
import { format } from 'util';

/**
 * Log levels
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  SILENT = 4
}

/**
 * Logger configuration interface
 */
export interface LoggerConfig {
  level: LogLevel;
  useColors: boolean;
  logToFile: boolean;
  logFilePath: string;
  includeTimestamp: boolean;
}

/**
 * Default logger configuration
 */
const defaultConfig: LoggerConfig = {
  level: LogLevel.INFO,
  useColors: true,
  logToFile: false,
  logFilePath: path.join(process.cwd(), 'agent.log'),
  includeTimestamp: true
};

/**
 * Logger class for consistent logging throughout the application
 */
class Logger {
  private config: LoggerConfig;
  private logStream: NodeJS.WritableStream | null = null;
  
  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
    this.setupFileLogging();
  }
  
  /**
   * Set up file logging if enabled
   */
  private setupFileLogging(): void {
    if (this.config.logToFile) {
      try {
        // Create log directory if it doesn't exist
        const logDir = path.dirname(this.config.logFilePath);
        fs.mkdir(logDir, { recursive: true }).catch(() => {});
        
        // Create write stream
        this.logStream = createWriteStream(this.config.logFilePath, { flags: 'a' });
      } catch (error) {
        console.error('Failed to set up file logging:', error);
      }
    }
  }
  
  /**
   * Configure the logger
   */
  public configure(config: Partial<LoggerConfig>): void {
    // Close existing log stream if changing file path
    if (this.logStream && config.logFilePath && config.logFilePath !== this.config.logFilePath) {
      this.logStream.end();
      this.logStream = null;
    }
    
    // Update config
    this.config = { ...this.config, ...config };
    
    // Set up file logging if needed
    if (this.config.logToFile && !this.logStream) {
      this.setupFileLogging();
    }
  }
  
  /**
   * Format a log message
   */
  private formatMessage(level: string, message: string): string {
    let formattedMessage = '';
    
    // Add timestamp if enabled
    if (this.config.includeTimestamp) {
      const timestamp = new Date().toISOString();
      formattedMessage += `[${timestamp}] `;
    }
    
    // Add log level
    formattedMessage += `[${level}] `;
    
    // Add message
    formattedMessage += message;
    
    return formattedMessage;
  }
  
  /**
   * Write a log message
   */
  private log(level: LogLevel, levelName: string, ...args: any[]): void {
    // Skip if log level is too low
    if (level < this.config.level) {
      return;
    }
    
    // Format message
    const message = format(...args);
    const formattedMessage = this.formatMessage(levelName, message);
    
    // Log to console
    switch (level) {
      case LogLevel.DEBUG:
        if (this.config.useColors) {
          console.debug(chalk.gray(formattedMessage));
        } else {
          console.debug(formattedMessage);
        }
        break;
      case LogLevel.INFO:
        if (this.config.useColors) {
          console.info(chalk.white(formattedMessage));
        } else {
          console.info(formattedMessage);
        }
        break;
      case LogLevel.WARN:
        if (this.config.useColors) {
          console.warn(chalk.yellow(formattedMessage));
        } else {
          console.warn(formattedMessage);
        }
        break;
      case LogLevel.ERROR:
        if (this.config.useColors) {
          console.error(chalk.red(formattedMessage));
        } else {
          console.error(formattedMessage);
        }
        break;
    }
    
    // Log to file if enabled
    if (this.config.logToFile && this.logStream) {
      this.logStream.write(formattedMessage + '\n');
    }
  }
  
  /**
   * Log a debug message
   */
  public debug(...args: any[]): void {
    this.log(LogLevel.DEBUG, 'DEBUG', ...args);
  }
  
  /**
   * Log an info message
   */
  public info(...args: any[]): void {
    this.log(LogLevel.INFO, 'INFO', ...args);
  }
  
  /**
   * Log a warning message
   */
  public warn(...args: any[]): void {
    this.log(LogLevel.WARN, 'WARN', ...args);
  }
  
  /**
   * Log an error message
   */
  public error(...args: any[]): void {
    this.log(LogLevel.ERROR, 'ERROR', ...args);
  }
  
  /**
   * Set the log level
   */
  public setLevel(level: LogLevel): void {
    this.config.level = level;
  }
  
  /**
   * Enable or disable color output
   */
  public setUseColors(useColors: boolean): void {
    this.config.useColors = useColors;
  }
  
  /**
   * Enable or disable file logging
   */
  public setLogToFile(logToFile: boolean, logFilePath?: string): void {
    this.config.logToFile = logToFile;
    
    if (logFilePath) {
      this.config.logFilePath = logFilePath;
    }
    
    // Set up file logging if enabled
    if (logToFile && !this.logStream) {
      this.setupFileLogging();
    } else if (!logToFile && this.logStream) {
      this.logStream.end();
      this.logStream = null;
    }
  }
  
  /**
   * Clean up resources
   */
  public dispose(): void {
    if (this.logStream) {
      this.logStream.end();
      this.logStream = null;
    }
  }

  /**
   * Update logger configuration based on config object
   */
  public updateFromConfig(config: any): void {
    // Set log level from config
    if (config.debug?.logLevel) {
      switch (config.debug.logLevel.toLowerCase()) {
        case 'debug':
          this.setLevel(LogLevel.DEBUG);
          break;
        case 'info':
          this.setLevel(LogLevel.INFO);
          break;
        case 'warn':
          this.setLevel(LogLevel.WARN);
          break;
        case 'error':
          this.setLevel(LogLevel.ERROR);
          break;
      }
    }

    // Set color usage from config
    if (config.ui?.theme === 'plain') {
      this.config.useColors = false;
    }
  }
}

// Create and export a singleton logger instance
export const logger = new Logger(); 